# NocoBase Collection 默认字段配置

## 概述

当创建 NocoBase collection 时，系统会根据配置自动添加以下默认字段：

## 默认字段列表

### 1. ID 字段 (id)
- **类型**: `bigInt`
- **属性**: 
  - `autoIncrement: true` - 自动递增
  - `primaryKey: true` - 主键
  - `allowNull: false` - 不允许为空
- **Interface**: `id`
- **UI Schema**:
  ```json
  {
    "type": "number",
    "title": "{{t(\"ID\")}}",
    "x-component": "InputNumber",
    "x-read-pretty": true
  }
  ```

### 2. 创建时间 (createdAt)
- **类型**: `date`
- **Interface**: `createdAt`
- **属性**: `field: "createdAt"`
- **UI Schema**:
  ```json
  {
    "type": "datetime",
    "title": "{{t(\"Created at\")}}",
    "x-component": "DatePicker",
    "x-component-props": {},
    "x-read-pretty": true
  }
  ```

### 3. 更新时间 (updatedAt)
- **类型**: `date`
- **Interface**: `updatedAt`
- **属性**: `field: "updatedAt"`
- **UI Schema**:
  ```json
  {
    "type": "datetime",
    "title": "{{t(\"Last updated at\")}}",
    "x-component": "DatePicker",
    "x-component-props": {},
    "x-read-pretty": true
  }
  ```

### 4. 创建人 (createdBy)
- **类型**: `belongsTo`
- **Interface**: `createdBy`
- **关联配置**:
  - `target: "users"` - 关联到用户表
  - `foreignKey: "createdById"` - 外键字段
- **UI Schema**:
  ```json
  {
    "type": "object",
    "title": "{{t(\"Created by\")}}",
    "x-component": "AssociationField",
    "x-component-props": {
      "fieldNames": {
        "value": "id",
        "label": "nickname"
      }
    },
    "x-read-pretty": true
  }
  ```

### 5. 更新人 (updatedBy)
- **类型**: `belongsTo`
- **Interface**: `updatedBy`
- **关联配置**:
  - `target: "users"` - 关联到用户表
  - `foreignKey: "updatedById"` - 外键字段
- **UI Schema**:
  ```json
  {
    "type": "object",
    "title": "{{t(\"Last updated by\")}}",
    "x-component": "AssociationField",
    "x-component-props": {
      "fieldNames": {
        "value": "id",
        "label": "nickname"
      }
    },
    "x-read-pretty": true
  }
  ```

## Collection 配置选项

在创建 collection 时，可以通过以下选项控制默认字段的生成：

```json
{
  "name": "collection_name",
  "title": "Collection Title",
  "autoGenId": true,      // 自动生成 ID 字段
  "createdAt": true,      // 添加创建时间字段
  "updatedAt": true,      // 添加更新时间字段
  "createdBy": true,      // 添加创建人字段
  "updatedBy": true,      // 添加更新人字段
  "fields": [
    // 自定义字段...
  ]
}
```

## 系统行为

1. **自动时间戳**: `createdAt` 和 `updatedAt` 字段会自动由数据库管理
2. **用户关联**: `createdBy` 和 `updatedBy` 字段会自动关联到当前登录用户
3. **外键字段**: 系统会自动创建 `createdById` 和 `updatedById` 外键字段
4. **只读显示**: 所有默认字段在 UI 中都设置为只读模式

## 测试验证

创建记录时的响应示例：
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 12,
    "createdAt": "2025-08-03T14:18:17.247Z",
    "updatedAt": "2025-08-03T14:18:17.247Z",
    "createdById": 1,
    "updatedById": 1
  }
}
```

获取记录时包含关联用户信息：
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "createdBy": {
      "id": 1,
      "nickname": "Super Admin",
      "username": "nocobase"
    },
    "updatedBy": {
      "id": 1,
      "nickname": "Super Admin",
      "username": "nocobase"
    }
  }
}
```
