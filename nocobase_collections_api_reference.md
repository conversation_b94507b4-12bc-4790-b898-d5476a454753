# NocoBase Collections API 参考文档

## 概述

NocoBase Collections API 提供了管理数据集合（collections）的完整接口，包括查询、创建、更新和删除集合及其字段的功能。

## 基础端点

### Collections 管理 API

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/collections:list` | GET | 获取所有集合列表（基础信息） |
| `/api/collections:listMeta` | GET | 获取所有集合列表（包含字段详情） |
| `/api/collections:get` | GET | 获取单个集合详情 |
| `/api/collections:create` | POST | 创建新集合 |
| `/api/collections:update` | POST | 更新集合配置 |
| `/api/collections:destroy` | POST | 删除集合 |

### Fields 管理 API

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/collections/{collection}/fields:list` | GET | 获取集合的所有字段 |
| `/api/collections/{collection}/fields:get` | GET | 获取单个字段详情 |
| `/api/collections/{collection}/fields:create` | POST | 为集合创建新字段 |
| `/api/collections/{collection}/fields:update` | POST | 更新字段配置 |
| `/api/collections/{collection}/fields:destroy` | POST | 删除字段 |

## 通用查询参数

所有查询操作支持以下参数：

| 参数 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `filterByTk` | string | 按主键过滤 | `filterByTk=students` |
| `filter` | object | 条件过滤器 | `filter[hidden]=false` |
| `sort` | string[] | 排序字段 | `sort[]=name` |
| `fields` | string[] | 选择返回字段 | `fields[]=name&fields[]=title` |
| `appends` | string[] | 包含关联数据 | `appends[]=fields` |
| `except` | string[] | 排除字段 | `except[]=description` |
| `page` | number | 页码（从1开始） | `page=1` |
| `pageSize` | number | 每页记录数 | `pageSize=20` |

## API 详细说明

### 1. 获取集合列表

**端点：** `GET /api/collections:list`

**参数：**
- 支持所有通用查询参数
- 常用过滤：`filter[hidden]=false` 排除隐藏集合

**请求示例：**
```bash
curl -X GET "https://your-domain/api/collections:list?pageSize=10&sort[]=name" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

**响应示例：**
```json
{
  "data": [
    {
      "key": "gq2m3dcm503",
      "name": "students",
      "title": "学生",
      "inherit": false,
      "hidden": false,
      "description": null,
      "autoGenId": true,
      "createdAt": true,
      "updatedAt": true,
      "createdBy": true,
      "updatedBy": true,
      "filterTargetKey": "id",
      "unavailableActions": []
    }
  ],
  "meta": {
    "count": 1,
    "page": 1,
    "pageSize": 10,
    "totalPage": 1
  }
}
```

### 2. 获取集合元数据列表（包含字段详情）

**端点：** `GET /api/collections:listMeta`

**描述：** 获取所有集合的详细元数据信息，包括每个集合的所有字段定义。这个端点返回的信息比 `collections:list` 更详细。

**参数：**
- 不支持分页参数
- 自动包含所有字段信息

**请求示例：**
```bash
curl -X GET "https://your-domain/api/collections:listMeta" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

**响应示例：**
```json
{
  "data": [
    {
      "origin": "@nocobase/plugin-data-source-main",
      "inherit": false,
      "hidden": false,
      "key": "gq2m3dcm503",
      "name": "students",
      "title": "学生",
      "sort": 3,
      "description": null,
      "fields": [
        {
          "name": "id",
          "key": "6vt6cme64zn",
          "type": "bigInt",
          "interface": "id",
          "collectionName": "students",
          "description": null,
          "autoIncrement": true,
          "primaryKey": true,
          "allowNull": false,
          "uiSchema": {
            "type": "number",
            "title": "{{t(\"ID\")}}",
            "x-component": "InputNumber",
            "x-read-pretty": true
          },
          "__sort": 1
        }
      ],
      "autoGenId": true,
      "createdAt": true,
      "updatedAt": true,
      "createdBy": true,
      "updatedBy": true,
      "loadedFromCollectionManager": true,
      "schema": "public",
      "dumpRules": {"group": "custom"},
      "filterTargetKey": "id",
      "unavailableActions": []
    }
  ]
}
```

### 3. 获取单个集合详情

**端点：** `GET /api/collections:get`

**参数：**
- `filterByTk` (必需): 集合名称
- `appends`: 可包含 `fields` 获取字段信息

**请求示例：**
```bash
curl -X GET "https://your-domain/api/collections:get?filterByTk=students&appends[]=fields" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

**响应示例：**
```json
{
  "data": {
    "key": "gq2m3dcm503",
    "name": "students",
    "title": "学生",
    "inherit": false,
    "hidden": false,
    "description": null,
    "fields": [
      {
        "key": "6vt6cme64zn",
        "name": "id",
        "type": "bigInt",
        "interface": "id",
        "autoIncrement": true,
        "primaryKey": true,
        "allowNull": false,
        "uiSchema": {
          "type": "number",
          "title": "{{t(\"ID\")}}",
          "x-component": "InputNumber",
          "x-read-pretty": true
        }
      }
    ],
    "autoGenId": true,
    "createdAt": true,
    "updatedAt": true,
    "createdBy": true,
    "updatedBy": true,
    "filterTargetKey": "id",
    "unavailableActions": []
  }
}
```

### 3. 创建集合

**端点：** `POST /api/collections:create`

**请求体参数：**
- `name` (必需): 集合名称
- `title`: 显示标题
- `autoGenId`: 是否自动生成ID字段
- `createdAt`: 是否添加创建时间字段
- `updatedAt`: 是否添加更新时间字段
- `createdBy`: 是否添加创建人字段
- `updatedBy`: 是否添加更新人字段
- `fields`: 字段定义数组

**请求示例：**
```bash
curl -X POST "https://your-domain/api/collections:create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>" \
  -d '{
    "name": "products",
    "title": "产品",
    "autoGenId": true,
    "createdAt": true,
    "updatedAt": true,
    "fields": [
      {
        "name": "name",
        "type": "string",
        "interface": "input",
        "uiSchema": {
          "type": "string",
          "title": "产品名称",
          "x-component": "Input",
          "required": true
        }
      }
    ]
  }'
```

### 4. 获取集合字段列表

**端点：** `GET /api/collections/{collection}/fields:list`

**参数：**
- `{collection}`: 集合名称
- 支持所有通用查询参数

**请求示例：**
```bash
curl -X GET "https://your-domain/api/collections/students/fields:list" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

**响应示例：**
```json
{
  "data": [
    {
      "key": "6vt6cme64zn",
      "name": "id",
      "type": "bigInt",
      "interface": "id",
      "description": null,
      "collectionName": "students",
      "autoIncrement": true,
      "primaryKey": true,
      "allowNull": false,
      "uiSchema": {
        "type": "number",
        "title": "{{t(\"ID\")}}",
        "x-component": "InputNumber",
        "x-read-pretty": true
      }
    }
  ],
  "meta": {
    "count": 10,
    "page": 1,
    "pageSize": 20,
    "totalPage": 1
  }
}
```

### 5. 为集合创建字段

**端点：** `POST /api/collections/{collection}/fields:create`

**请求体参数：**
- `name` (必需): 字段名称
- `type` (必需): 字段类型
- `interface`: 界面类型
- `uiSchema`: UI 配置
- 其他字段特定参数

**请求示例：**
```bash
curl -X POST "https://your-domain/api/collections/students/fields:create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>" \
  -d '{
    "name": "email",
    "type": "string",
    "interface": "email",
    "uiSchema": {
      "type": "string",
      "title": "邮箱",
      "x-component": "Input",
      "format": "email"
    }
  }'
```

## 认证和权限

所有 API 请求需要在 Header 中包含：

```http
Authorization: Bearer <your-jwt-token>
X-App: <app-name>
```

## 错误响应格式

```json
{
  "errors": [
    {
      "message": "错误信息描述"
    }
  ]
}
```

## 常见使用场景

### 1. 查看系统中所有非隐藏集合
```bash
curl -X GET "https://your-domain/api/collections:list?filter[hidden]=false"
```

### 2. 获取集合的完整信息（包含字段）
```bash
curl -X GET "https://your-domain/api/collections:get?filterByTk=students&appends[]=fields"
```

### 3. 查看集合的所有字段详情
```bash
curl -X GET "https://your-domain/api/collections/students/fields:list"
```

### 4. 删除集合
```bash
curl -X POST "https://your-domain/api/collections:destroy?filterByTk=students"
```

## 注意事项

1. **权限控制**: 需要相应的管理员权限才能执行集合和字段的创建、更新、删除操作
2. **数据完整性**: 删除集合会同时删除所有相关数据，请谨慎操作
3. **字段依赖**: 某些字段可能被其他集合引用，删除前需要检查依赖关系
4. **缓存更新**: 集合结构变更后可能需要重启应用或清除缓存
