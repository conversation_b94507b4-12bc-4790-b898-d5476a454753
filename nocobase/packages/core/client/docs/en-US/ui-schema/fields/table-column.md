# 表格列

## UI Schema

### 表格列

```json
{
  "_isJSONSchemaObject": true,
  "version": "2.0",
  "type": "void",
  "x-decorator": "TableV2.Column.Decorator",
  "x-designer": "TableV2.Column.Designer",
  "x-component": "TableV2.Column",
  "properties": {
    "f_beilggsbj0r": {
      "_isJSONSchemaObject": true,
      "version": "2.0",
      "x-collection-field": "a.f_beilggsbj0r",
      "x-component": "CollectionField",
      "x-component-props": {
        "ellipsis": true
      },
      "x-read-pretty": true,
      "x-decorator": null,
      "x-decorator-props": {
        "labelStyle": {
          "display": "none"
        }
      },
      "x-uid": "h5u4xi7uvji",
      "x-async": false,
      "x-index": 1
    }
  },
  "x-uid": "z8p8ezctwsh",
  "x-async": false,
  "x-index": 2
}
```
