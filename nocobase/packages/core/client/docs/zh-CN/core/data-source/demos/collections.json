[{"key": "h7b9i8khc3q", "name": "users", "inherit": false, "hidden": false, "description": null, "category": [], "namespace": "users.users", "duplicator": {"dumpable": "optional", "with": "rolesUsers"}, "sortable": "sort", "model": "UserModel", "createdBy": true, "updatedBy": true, "logging": true, "from": "db2cm", "title": "{{t(\"Users\")}}", "rawTitle": "{{t(\"Users\")}}", "fields": [{"uiSchema": {"type": "number", "title": "{{t(\"ID\")}}", "x-component": "InputNumber", "x-read-pretty": true, "rawTitle": "{{t(\"ID\")}}"}, "key": "ffp1f2sula0", "name": "id", "type": "bigInt", "interface": "id", "description": null, "collectionName": "users", "parentKey": null, "reverseKey": null, "autoIncrement": true, "primaryKey": true, "allowNull": false}, {"uiSchema": {"type": "string", "title": "{{t(\"Nick<PERSON>\")}}", "x-component": "Input", "rawTitle": "{{t(\"Nick<PERSON>\")}}"}, "key": "vrv7yjue90g", "name": "nickname", "type": "string", "interface": "input", "description": null, "collectionName": "users", "parentKey": null, "reverseKey": null}, {"uiSchema": {"type": "string", "title": "{{t(\"<PERSON>rname\")}}", "x-component": "Input", "x-validator": {"username": true}, "required": true, "rawTitle": "{{t(\"<PERSON>rname\")}}"}, "key": "2ccs6evyrub", "name": "username", "type": "string", "interface": "input", "description": null, "collectionName": "users", "parentKey": null, "reverseKey": null, "unique": true}, {"uiSchema": {"type": "string", "title": "{{t(\"Email\")}}", "x-component": "Input", "x-validator": "email", "required": true, "rawTitle": "{{t(\"Email\")}}"}, "key": "rrskwjl5wt1", "name": "email", "type": "string", "interface": "email", "description": null, "collectionName": "users", "parentKey": null, "reverseKey": null, "unique": true}, {"key": "t09bauwm0wb", "name": "roles", "type": "belongsToMany", "interface": "m2m", "description": null, "collectionName": "users", "parentKey": null, "reverseKey": null, "target": "roles", "foreignKey": "userId", "otherKey": "<PERSON><PERSON><PERSON>", "onDelete": "CASCADE", "sourceKey": "id", "targetKey": "name", "through": "rolesUsers", "uiSchema": {"type": "array", "title": "{{t(\"Roles\")}}", "x-component": "AssociationField", "x-component-props": {"multiple": true, "fieldNames": {"label": "title", "value": "name"}}}}]}, {"key": "pqnenvqrzxr", "name": "roles", "inherit": false, "hidden": false, "description": null, "category": [], "namespace": "acl.acl", "duplicator": {"dumpable": "required", "with": "uiSchemas"}, "autoGenId": false, "model": "RoleModel", "filterTargetKey": "name", "sortable": true, "from": "db2cm", "title": "{{t(\"Roles\")}}", "rawTitle": "{{t(\"Roles\")}}", "fields": [{"uiSchema": {"type": "string", "title": "{{t(\"Role UID\")}}", "x-component": "Input", "rawTitle": "{{t(\"Role UID\")}}"}, "key": "jbz9m80bxmp", "name": "name", "type": "uid", "interface": "input", "description": null, "collectionName": "roles", "parentKey": null, "reverseKey": null, "prefix": "r_", "primaryKey": true}, {"uiSchema": {"type": "string", "title": "{{t(\"Role name\")}}", "x-component": "Input", "rawTitle": "{{t(\"Role name\")}}"}, "key": "faywtz4sf3u", "name": "title", "type": "string", "interface": "input", "description": null, "collectionName": "roles", "parentKey": null, "reverseKey": null, "unique": true, "translation": true}, {"key": "1enkovm9sye", "name": "description", "type": "string", "interface": null, "description": null, "collectionName": "roles", "parentKey": null, "reverseKey": null}]}]