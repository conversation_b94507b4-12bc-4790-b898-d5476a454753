# Markdown

## UI Schema

```json
{
  "type": "void",
  "version": "2.0",
  "x-designer": "Markdown.Void.Designer",
  "x-editable": false,
  "x-component": "Markdown.Void",
  "x-decorator": "CardItem",
  "x-component-props": {
    "content": "This is a demo text, **supports Markdown syntax**."
  },
  "x-decorator-props": {
    "name": "markdown"
  },
  "_isJSONSchemaObject": true,
  "x-uid": "w75e9d5vs1e",
  "x-async": false,
  "x-index": 1
}
```