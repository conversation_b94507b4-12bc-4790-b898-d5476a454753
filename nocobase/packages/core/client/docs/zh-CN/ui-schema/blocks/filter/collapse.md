# Collapse

## UI Schema

### 折叠面板

```json
{
  "_isJSONSchemaObject": true,
  "version": "2.0",
  "type": "void",
  "x-decorator": "AssociationFilter.Provider",
  "x-decorator-props": {
    "collection": "users",
    "blockType": "filter",
    "associationFilterStyle": {
      "width": "100%"
    },
    "name": "filter-collapse"
  },
  "x-designer": "AssociationFilter.BlockDesigner",
  "x-component": "CardItem",
  "x-filter-targets": [],
  "properties": {
    "x8q0r7wp73e": {
      "_isJSONSchemaObject": true,
      "version": "2.0",
      "type": "void",
      "x-action": "associateFilter",
      "x-initializer": "AssociationFilter.FilterBlockInitializer",
      "x-component": "AssociationFilter",
      "x-uid": "j11suxuizte",
      "x-async": false,
      "x-index": 1
    }
  },
  "x-uid": "0g6pyonrj83",
  "x-async": false,
  "x-index": 1
}
```