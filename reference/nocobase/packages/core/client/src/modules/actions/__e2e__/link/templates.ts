/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const oneEmptyTableWithUsers = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.0.0-alpha.17',
    properties: {
      hoy3gninc4d: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.0.0-alpha.17',
        properties: {
          '6q8k2r2zg8b': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.17',
            properties: {
              '5pelgrmw1uv': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.17',
                properties: {
                  jinafp4khmd: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.0.0-alpha.17',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.0.0-alpha.17',
                        'x-uid': '57i5fodavmy',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '1c78hfblr62': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.0.0-alpha.17',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-designer': 'TableV2.ActionColumnDesigner',
                            'x-initializer': 'table:configureItemActions',
                            'x-app-version': '1.0.0-alpha.17',
                            properties: {
                              sq7wrsmu8k6: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.0.0-alpha.17',
                                'x-uid': '9wjvgdr7qlp',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'c2kd07xlalt',
                            'x-async': false,
                            'x-index': 1,
                          },
                          kce2z62jj3e: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.0-alpha.17',
                            properties: {
                              id: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.id',
                                'x-component': 'CollectionField',
                                'x-component-props': {},
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.0-alpha.17',
                                'x-uid': '5dwr9au92jy',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'd3o6bo2bntr',
                            'x-async': false,
                            'x-index': 2,
                          },
                          cxivkl2pz2e: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.0-alpha.17',
                            properties: {
                              username: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.username',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.0-alpha.17',
                                'x-uid': '6zd6cdztudd',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'bstw44sol77',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'tesn3o6xlvo',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'am04rxkwyrn',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'l6m3f6u2opr',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'tgnms5xwgtj',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'cgq9lp3869e',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'py05yxxke4g',
    'x-async': true,
    'x-index': 1,
  },
};
export const openInNewWidow = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.2.21-alpha',
    properties: {
      g3i4gd8j68w: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.2.21-alpha',
        properties: {
          k0tx69u4ge5: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.2.21-alpha',
            properties: {
              hck9q8g9nfw: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.2.21-alpha',
                properties: {
                  jq1vb4hd0vk: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.2.21-alpha',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.2.21-alpha',
                        'x-uid': 'n7hoqcgj7nn',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '068dk7k35nk': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.2.21-alpha',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.2.21-alpha',
                            properties: {
                              mo7685lm8vz: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.2.21-alpha',
                                properties: {
                                  t4s3iem6ael: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: '{{ t("Link") }}',
                                    'x-action': 'customize:link',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:link',
                                    'x-component': 'Action.Link',
                                    'x-use-component-props': 'useLinkActionProps',
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    'x-uid': 'pvrerrk3t7w',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'cih8h4b2193',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '50yfcnetw5a',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': '4q78sr1a4so',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'k38ru1y7s6s',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'a0aiamh41on',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'gn2c5n843iv',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'gvbup8mmfql',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'af02n5ipxrk',
    'x-async': true,
    'x-index': 1,
  },
};
export const PopupAndSubPageWithParams = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      b1atmxyqbff: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          rkebzj98nvq: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.2.11-alpha',
            properties: {
              kxz1o673sem: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.2.11-alpha',
                properties: {
                  t79t7o2scw5: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.2.11-alpha',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.2.11-alpha',
                        'x-uid': 'm3lmb6s616o',
                        'x-async': false,
                        'x-index': 1,
                      },
                      sghz67ot4pa: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.2.11-alpha',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.2.11-alpha',
                            properties: {
                              ebq18cea3uo: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.2.11-alpha',
                                properties: {
                                  c14cpxkc4ke: {
                                    'x-uid': 'ech5j6ogus0',
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: '{{ t("View") }}',
                                    'x-action': 'view',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:view',
                                    'x-component': 'Action.Link',
                                    'x-component-props': {
                                      openMode: 'drawer',
                                    },
                                    'x-action-context': {
                                      dataSource: 'main',
                                      collection: 'users',
                                    },
                                    'x-decorator': 'ACLActionProvider',
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    properties: {
                                      drawer: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        title: '{{ t("View record") }}',
                                        'x-component': 'Action.Container',
                                        'x-component-props': {
                                          className: 'nb-action-popup',
                                        },
                                        properties: {
                                          tabs: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'void',
                                            'x-component': 'Tabs',
                                            'x-component-props': {},
                                            'x-initializer': 'popup:addTab',
                                            properties: {
                                              tab1: {
                                                _isJSONSchemaObject: true,
                                                version: '2.0',
                                                type: 'void',
                                                title: '{{t("Details")}}',
                                                'x-component': 'Tabs.TabPane',
                                                'x-designer': 'Tabs.Designer',
                                                'x-component-props': {},
                                                properties: {
                                                  grid: {
                                                    _isJSONSchemaObject: true,
                                                    version: '2.0',
                                                    type: 'void',
                                                    'x-component': 'Grid',
                                                    'x-initializer': 'popup:common:addBlock',
                                                    properties: {
                                                      z1h7wnh6s60: {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '1.2.11-alpha',
                                                        properties: {
                                                          bxzkcif7vu9: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '1.2.11-alpha',
                                                            properties: {
                                                              e2pjbwaou03: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action-props': {
                                                                  skipScopeCheck: true,
                                                                },
                                                                'x-acl-action': 'users:create',
                                                                'x-decorator': 'FormBlockProvider',
                                                                'x-use-decorator-props':
                                                                  'useCreateFormBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  dataSource: 'main',
                                                                  collection: 'users',
                                                                  isCusomeizeCreate: true,
                                                                },
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:createForm',
                                                                'x-component': 'CardItem',
                                                                'x-app-version': '1.2.11-alpha',
                                                                properties: {
                                                                  j86fmzva2n0: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-component': 'FormV2',
                                                                    'x-use-component-props': 'useCreateFormBlockProps',
                                                                    'x-app-version': '1.2.11-alpha',
                                                                    properties: {
                                                                      grid: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-component': 'Grid',
                                                                        'x-initializer': 'form:configureFields',
                                                                        'x-app-version': '1.2.11-alpha',
                                                                        properties: {
                                                                          s1cu9qfbyx6: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid.Row',
                                                                            'x-app-version': '1.2.11-alpha',
                                                                            properties: {
                                                                              rmck9ducuw7: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Col',
                                                                                'x-app-version': '1.2.11-alpha',
                                                                                properties: {
                                                                                  nickname: {
                                                                                    'x-uid': '22a0rhtgo1x',
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'string',
                                                                                    'x-toolbar':
                                                                                      'FormItemSchemaToolbar',
                                                                                    'x-settings':
                                                                                      'fieldSettings:FormItem',
                                                                                    'x-component': 'CollectionField',
                                                                                    'x-decorator': 'FormItem',
                                                                                    'x-collection-field':
                                                                                      'users.nickname',
                                                                                    'x-component-props': {},
                                                                                    'x-app-version': '1.2.11-alpha',
                                                                                    default:
                                                                                      '{{$nURLSearchParams.name}}',
                                                                                    'x-async': false,
                                                                                    'x-index': 1,
                                                                                  },
                                                                                },
                                                                                'x-uid': 'eudbw9veltu',
                                                                                'x-async': false,
                                                                                'x-index': 1,
                                                                              },
                                                                            },
                                                                            'x-uid': 'jdt8bqzqi2s',
                                                                            'x-async': false,
                                                                            'x-index': 1,
                                                                          },
                                                                        },
                                                                        'x-uid': '5hq25kdeet9',
                                                                        'x-async': false,
                                                                        'x-index': 1,
                                                                      },
                                                                      kke3cg70wha: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-initializer': 'createForm:configureActions',
                                                                        'x-component': 'ActionBar',
                                                                        'x-component-props': {
                                                                          layout: 'one-column',
                                                                        },
                                                                        'x-app-version': '1.2.11-alpha',
                                                                        'x-uid': 'usbcj3rxwdq',
                                                                        'x-async': false,
                                                                        'x-index': 2,
                                                                      },
                                                                    },
                                                                    'x-uid': 'xk49vnplykb',
                                                                    'x-async': false,
                                                                    'x-index': 1,
                                                                  },
                                                                },
                                                                'x-uid': 'xz0fzo8aiwd',
                                                                'x-async': false,
                                                                'x-index': 1,
                                                              },
                                                            },
                                                            'x-uid': 'c2z7nkp1er8',
                                                            'x-async': false,
                                                            'x-index': 1,
                                                          },
                                                        },
                                                        'x-uid': '817scrzocq2',
                                                        'x-async': false,
                                                        'x-index': 1,
                                                      },
                                                    },
                                                    'x-uid': 'o5aa5p4qdfy',
                                                    'x-async': false,
                                                    'x-index': 1,
                                                  },
                                                },
                                                'x-uid': 'cyys2ghczmx',
                                                'x-async': false,
                                                'x-index': 1,
                                              },
                                            },
                                            'x-uid': 'pte28pwapoh',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'nnc6fz5v38k',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'nba9caa8pc1',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '14vqyxwyyty',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'tu8n9op1z9r',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'v2gig2bsyex',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '9r7wta3gbml',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'zj3df6jm0dh',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'fz1jj9rlu87',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'de0k4foh9cc',
    'x-async': true,
    'x-index': 1,
  },
};
export const URLSearchParamsUseAssociationFieldValue = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.3.33-beta',
    properties: {
      gbfvfwym8ds: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.3.33-beta',
        properties: {
          '33bst9cikf7': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.3.33-beta',
            properties: {
              fewb4k72bc8: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.3.33-beta',
                properties: {
                  '5cfypzqvw57': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.3.33-beta',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.3.33-beta',
                        'x-uid': '1xy1166yc9k',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '6a59q3gjjqu': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.3.33-beta',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.3.33-beta',
                            properties: {
                              lzxiek232g3: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.3.33-beta',
                                properties: {
                                  qhrjv5sk1tc: {
                                    'x-uid': 'o7zrp842yhz',
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: '{{ t("Link") }}',
                                    'x-action': 'customize:link',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:link',
                                    'x-component': 'Action.Link',
                                    'x-use-component-props': 'useLinkActionProps',
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    'x-component-props': {
                                      url: '/admin/ocal3pnltf2',
                                      params: [
                                        {
                                          name: 'roles',
                                          value: '{{$nRecord.roles}}',
                                        },
                                      ],
                                    },
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'y0m958j0dh0',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '4d4wnl1b6xx',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': '238yee6oghy',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'gjzmrbmobaf',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'z4rsa4oitvz',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '6tec1zys03w',
            'x-async': false,
            'x-index': 1,
          },
          sbdgc6nmy9x: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.3.33-beta',
            properties: {
              '3sfuujjet76': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.3.33-beta',
                properties: {
                  p3zmu2uua0x: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action-props': {
                      skipScopeCheck: true,
                    },
                    'x-acl-action': 'users:create',
                    'x-decorator': 'FormBlockProvider',
                    'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:createForm',
                    'x-component': 'CardItem',
                    'x-app-version': '1.3.33-beta',
                    properties: {
                      '1oq2w1ia4jy': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useCreateFormBlockProps',
                        'x-app-version': '1.3.33-beta',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'form:configureFields',
                            'x-app-version': '1.3.33-beta',
                            properties: {
                              efdbxp35iht: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.3.33-beta',
                                properties: {
                                  k40ivzy5kcu: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.3.33-beta',
                                    properties: {
                                      roles: {
                                        'x-uid': 'v8jvm1d9j8q',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.roles',
                                        'x-component-props': {
                                          fieldNames: {
                                            label: 'name',
                                            value: 'name',
                                          },
                                        },
                                        'x-app-version': '1.3.33-beta',
                                        default: '{{$nURLSearchParams.roles}}',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '3rs4fwe2gak',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'tuqcvp6tzbg',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'dz9070niyqm',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '5mu8w85umxn': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'createForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                            },
                            'x-app-version': '1.3.33-beta',
                            'x-uid': 'xgak2t61ukm',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'asfu0o75c3k',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'tgsr3gv33qk',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'pjd4g9evi3v',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'hj4wq3bdtip',
            'x-async': false,
            'x-index': 2,
          },
        },
        'x-uid': '1xgpa64dn71',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'ocal3pnltf2',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
  pageUid: 'ids0d9esx8k',
};
