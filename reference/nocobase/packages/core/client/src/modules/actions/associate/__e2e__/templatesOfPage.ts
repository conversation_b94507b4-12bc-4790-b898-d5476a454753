/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

import { PageConfig } from '@nocobase/test/e2e';

export const associatePage: PageConfig = {
  collections: [
    {
      key: 'x3cx2q5s9jb',
      name: 'aa',
      title: 'aa',
      inherit: false,
      hidden: false,
      description: null,
      fields: [
        {
          key: 'cbkrcpobknq',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
          allowNull: false,
          primaryKey: true,
          autoIncrement: true,
        },
        {
          key: 'xnpk8j33ulw',
          name: 'createdAt',
          type: 'date',
          interface: 'createdAt',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          field: 'createdAt',
          uiSchema: {
            type: 'datetime',
            title: '{{t("Created at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: 'tond37ffjc9',
          name: 'createdBy',
          type: 'belongsTo',
          interface: 'createdBy',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Created by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'createdById',
        },
        {
          key: '8mbaba8dhs4',
          name: 'updatedAt',
          type: 'date',
          interface: 'updatedAt',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          field: 'updatedAt',
          uiSchema: {
            type: 'string',
            title: '{{t("Last updated at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: '06hqse9mkpg',
          name: 'updatedBy',
          type: 'belongsTo',
          interface: 'updatedBy',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Last updated by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'updatedById',
        },
        {
          key: 'bl2zul81ju5',
          name: 'oho',
          type: 'hasOne',
          interface: 'oho',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          target: 'bb',
          onDelete: 'SET NULL',
          uiSchema: {
            title: 'oho',
            'x-component': 'AssociationField',
            'x-component-props': {
              multiple: false,
            },
          },
          sourceKey: 'id',
          foreignKey: 'f_5lfr6jf0plq',
        },
        {
          key: 'phzo162xtyy',
          name: 'o2m',
          type: 'hasMany',
          interface: 'o2m',
          description: null,
          collectionName: 'aa',
          parentKey: null,
          reverseKey: null,
          target: 'cc',
          onDelete: 'SET NULL',
          uiSchema: {
            title: 'o2m',
            'x-component': 'AssociationField',
            'x-component-props': {
              multiple: true,
            },
          },
          sourceKey: 'id',
          targetKey: 'id',
          foreignKey: 'f_ihh9xdg73i2',
        },
      ],
      category: [],
      view: false,
      logging: true,
      template: 'general',
      autoGenId: false,
      createdAt: true,
      createdBy: true,
      updatedAt: true,
      updatedBy: true,
    },
    {
      key: 'zdsqyp62fpn',
      name: 'bb',
      title: 'bb',
      inherit: false,
      hidden: false,
      description: null,
      fields: [
        {
          key: 'fxzdkhcb5zd',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
          allowNull: false,
          primaryKey: true,
          autoIncrement: true,
        },
        {
          key: '1a3swiada99',
          name: 'f_5lfr6jf0plq',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          uiSchema: {
            type: 'number',
            title: 'f_5lfr6jf0plq',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
          isForeignKey: true,
        },
        {
          key: '051f64vtjob',
          name: 'createdAt',
          type: 'date',
          interface: 'createdAt',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          field: 'createdAt',
          uiSchema: {
            type: 'datetime',
            title: '{{t("Created at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: 'hygm1txdp35',
          name: 'createdBy',
          type: 'belongsTo',
          interface: 'createdBy',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Created by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'createdById',
        },
        {
          key: 'd8knhubg6th',
          name: 'updatedAt',
          type: 'date',
          interface: 'updatedAt',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          field: 'updatedAt',
          uiSchema: {
            type: 'string',
            title: '{{t("Last updated at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: '6k7bwl42r5f',
          name: 'updatedBy',
          type: 'belongsTo',
          interface: 'updatedBy',
          description: null,
          collectionName: 'bb',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Last updated by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'updatedById',
        },
      ],
      category: [],
      view: false,
      logging: true,
      template: 'general',
      autoGenId: false,
      createdAt: true,
      createdBy: true,
      updatedAt: true,
      updatedBy: true,
    },
    {
      key: 'lqfsgwnlcpc',
      name: 'cc',
      title: 'cc',
      inherit: false,
      hidden: false,
      description: null,
      fields: [
        {
          key: 'r7tvpikklrn',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
          allowNull: false,
          primaryKey: true,
          autoIncrement: true,
        },
        {
          key: 'lrgeycd4qfg',
          name: 'f_ihh9xdg73i2',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          uiSchema: {
            type: 'number',
            title: 'f_ihh9xdg73i2',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
          isForeignKey: true,
        },
        {
          key: 'fj7q773tlya',
          name: 'createdAt',
          type: 'date',
          interface: 'createdAt',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          field: 'createdAt',
          uiSchema: {
            type: 'datetime',
            title: '{{t("Created at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: 'rsyjh5e151s',
          name: 'createdBy',
          type: 'belongsTo',
          interface: 'createdBy',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Created by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'createdById',
        },
        {
          key: '9uefpuansmt',
          name: 'updatedAt',
          type: 'date',
          interface: 'updatedAt',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          field: 'updatedAt',
          uiSchema: {
            type: 'string',
            title: '{{t("Last updated at")}}',
            'x-component': 'DatePicker',
            'x-read-pretty': true,
            'x-component-props': {},
          },
        },
        {
          key: 'oyq93f6dmxv',
          name: 'updatedBy',
          type: 'belongsTo',
          interface: 'updatedBy',
          description: null,
          collectionName: 'cc',
          parentKey: null,
          reverseKey: null,
          target: 'users',
          uiSchema: {
            type: 'object',
            title: '{{t("Last updated by")}}',
            'x-component': 'AssociationField',
            'x-read-pretty': true,
            'x-component-props': {
              fieldNames: {
                label: 'nickname',
                value: 'id',
              },
            },
          },
          targetKey: 'id',
          foreignKey: 'updatedById',
        },
      ],
      category: [],
      view: false,
      logging: true,
      template: 'general',
      autoGenId: false,
      createdAt: true,
      createdBy: true,
      updatedAt: true,
      updatedBy: true,
    },
  ],
  pageSchema: {
    type: 'void',
    version: '2.0',
    'x-component': 'Page',
    'x-app-version': '1.5.0-alpha.4',
    _isJSONSchemaObject: true,
    properties: {
      '0iepcnlrln6': {
        type: 'void',
        version: '2.0',
        'x-component': 'Grid',
        'x-app-version': '1.5.0-alpha.4',
        'x-initializer': 'page:addBlock',
        _isJSONSchemaObject: true,
        properties: {
          '0r23y3jdtqe': {
            type: 'void',
            version: '2.0',
            'x-component': 'Grid.Row',
            'x-app-version': '1.5.0-alpha.4',
            _isJSONSchemaObject: true,
            properties: {
              zgq7y2727pp: {
                type: 'void',
                version: '2.0',
                'x-component': 'Grid.Col',
                'x-app-version': '1.5.0-alpha.4',
                _isJSONSchemaObject: true,
                properties: {
                  qaw96fbyazy: {
                    type: 'void',
                    version: '2.0',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'aa:list',
                    'x-app-version': '1.5.0-alpha.4',
                    'x-filter-targets': [],
                    'x-decorator-props': {
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      dragSort: false,
                      showIndex: true,
                      collection: 'aa',
                      dataSource: 'main',
                    },
                    _isJSONSchemaObject: true,
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    properties: {
                      actions: {
                        type: 'void',
                        version: '2.0',
                        'x-component': 'ActionBar',
                        'x-app-version': '1.5.0-alpha.4',
                        'x-initializer': 'table:configureActions',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        _isJSONSchemaObject: true,
                        'x-uid': 'njmnmmdvdow',
                        'x-async': false,
                        'x-index': 1,
                      },
                      db93z47btxp: {
                        type: 'array',
                        version: '2.0',
                        'x-component': 'TableV2',
                        'x-app-version': '1.5.0-alpha.4',
                        'x-initializer': 'table:configureColumns',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        _isJSONSchemaObject: true,
                        'x-use-component-props': 'useTableBlockProps',
                        properties: {
                          actions: {
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            version: '2.0',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-app-version': '1.5.0-alpha.4',
                            'x-initializer': 'table:configureItemActions',
                            'x-action-column': 'actions',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            _isJSONSchemaObject: true,
                            properties: {
                              '3vjh5mn0uij': {
                                type: 'void',
                                version: '2.0',
                                'x-component': 'Space',
                                'x-decorator': 'DndContext',
                                'x-app-version': '1.5.0-alpha.4',
                                'x-component-props': {
                                  split: '|',
                                },
                                _isJSONSchemaObject: true,
                                properties: {
                                  hkzr1p0zm8j: {
                                    type: 'void',
                                    title: '{{ t("Edit") }}',
                                    version: '2.0',
                                    'x-action': 'update',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:edit',
                                    'x-component': 'Action.Link',
                                    'x-decorator': 'ACLActionProvider',
                                    'x-action-context': {
                                      collection: 'aa',
                                      dataSource: 'main',
                                    },
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    'x-component-props': {
                                      icon: 'EditOutlined',
                                      openMode: 'drawer',
                                    },
                                    _isJSONSchemaObject: true,
                                    properties: {
                                      drawer: {
                                        type: 'void',
                                        title: '{{ t("Edit record") }}',
                                        version: '2.0',
                                        'x-component': 'Action.Container',
                                        'x-component-props': {
                                          className: 'nb-action-popup',
                                        },
                                        _isJSONSchemaObject: true,
                                        properties: {
                                          tabs: {
                                            type: 'void',
                                            version: '2.0',
                                            'x-component': 'Tabs',
                                            'x-initializer': 'popup:addTab',
                                            'x-component-props': {},
                                            _isJSONSchemaObject: true,
                                            properties: {
                                              tab1: {
                                                type: 'void',
                                                title: '{{t("Edit")}}',
                                                version: '2.0',
                                                'x-designer': 'Tabs.Designer',
                                                'x-component': 'Tabs.TabPane',
                                                'x-component-props': {},
                                                _isJSONSchemaObject: true,
                                                properties: {
                                                  grid: {
                                                    type: 'void',
                                                    version: '2.0',
                                                    'x-component': 'Grid',
                                                    'x-initializer': 'popup:common:addBlock',
                                                    _isJSONSchemaObject: true,
                                                    properties: {
                                                      islzm2p3fyl: {
                                                        type: 'void',
                                                        version: '2.0',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '1.5.0-alpha.4',
                                                        _isJSONSchemaObject: true,
                                                        properties: {
                                                          '05q7b3guzf8': {
                                                            type: 'void',
                                                            version: '2.0',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '1.5.0-alpha.4',
                                                            _isJSONSchemaObject: true,
                                                            properties: {
                                                              '50m357nba9d': {
                                                                type: 'void',
                                                                version: '2.0',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:details',
                                                                'x-component': 'CardItem',
                                                                'x-decorator': 'DetailsBlockProvider',
                                                                'x-acl-action': 'aa.oho:get',
                                                                'x-app-version': '1.5.0-alpha.4',
                                                                'x-decorator-props': {
                                                                  action: 'get',
                                                                  dataSource: 'main',
                                                                  readPretty: true,
                                                                  association: 'aa.oho',
                                                                },
                                                                _isJSONSchemaObject: true,
                                                                'x-use-decorator-props': 'useDetailsDecoratorProps',
                                                                properties: {
                                                                  '86hvkw7yxo5': {
                                                                    type: 'void',
                                                                    version: '2.0',
                                                                    'x-component': 'Details',
                                                                    'x-app-version': '1.5.0-alpha.4',
                                                                    'x-read-pretty': true,
                                                                    _isJSONSchemaObject: true,
                                                                    'x-use-component-props': 'useDetailsProps',
                                                                    properties: {
                                                                      kse1vzd1ber: {
                                                                        type: 'void',
                                                                        version: '2.0',
                                                                        'x-component': 'ActionBar',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        'x-initializer': 'details:configureActions',
                                                                        'x-component-props': {
                                                                          style: {
                                                                            marginBottom: 24,
                                                                          },
                                                                        },
                                                                        _isJSONSchemaObject: true,
                                                                        'x-uid': '1qjc7vjttje',
                                                                        'x-async': false,
                                                                        'x-index': 1,
                                                                      },
                                                                      grid: {
                                                                        type: 'void',
                                                                        version: '2.0',
                                                                        'x-component': 'Grid',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        'x-initializer': 'details:configureFields',
                                                                        _isJSONSchemaObject: true,
                                                                        'x-uid': '2kyp1hl31mw',
                                                                        'x-async': false,
                                                                        'x-index': 2,
                                                                      },
                                                                    },
                                                                    'x-uid': '84hg8famonu',
                                                                    'x-async': false,
                                                                    'x-index': 1,
                                                                  },
                                                                },
                                                                'x-uid': 'q05v3akvvci',
                                                                'x-async': false,
                                                                'x-index': 1,
                                                              },
                                                            },
                                                            'x-uid': 'ribr89xe767',
                                                            'x-async': false,
                                                            'x-index': 1,
                                                          },
                                                        },
                                                        'x-uid': 'lmtsuh86p1z',
                                                        'x-async': false,
                                                        'x-index': 1,
                                                      },
                                                      n7wzrckhwks: {
                                                        type: 'void',
                                                        version: '2.0',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '1.5.0-alpha.4',
                                                        _isJSONSchemaObject: true,
                                                        properties: {
                                                          '6y88u7cazzh': {
                                                            type: 'void',
                                                            version: '2.0',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '1.5.0-alpha.4',
                                                            _isJSONSchemaObject: true,
                                                            properties: {
                                                              se4f0tcqxyj: {
                                                                type: 'void',
                                                                version: '2.0',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:detailsWithPagination',
                                                                'x-component': 'CardItem',
                                                                'x-decorator': 'DetailsBlockProvider',
                                                                'x-acl-action': 'aa.o2m:view',
                                                                'x-app-version': '1.5.0-alpha.4',
                                                                'x-decorator-props': {
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 1,
                                                                  },
                                                                  dataSource: 'main',
                                                                  readPretty: true,
                                                                  association: 'aa.o2m',
                                                                },
                                                                _isJSONSchemaObject: true,
                                                                'x-use-decorator-props':
                                                                  'useDetailsWithPaginationDecoratorProps',
                                                                properties: {
                                                                  ijifp1g2k5w: {
                                                                    type: 'void',
                                                                    version: '2.0',
                                                                    'x-component': 'Details',
                                                                    'x-app-version': '1.5.0-alpha.4',
                                                                    'x-read-pretty': true,
                                                                    _isJSONSchemaObject: true,
                                                                    'x-use-component-props':
                                                                      'useDetailsWithPaginationProps',
                                                                    properties: {
                                                                      ruu3kgm8blx: {
                                                                        type: 'void',
                                                                        version: '2.0',
                                                                        'x-component': 'ActionBar',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        'x-initializer': 'details:configureActions',
                                                                        'x-component-props': {
                                                                          style: {
                                                                            marginBottom: 24,
                                                                          },
                                                                        },
                                                                        _isJSONSchemaObject: true,
                                                                        'x-uid': '50qm1e5retr',
                                                                        'x-async': false,
                                                                        'x-index': 1,
                                                                      },
                                                                      grid: {
                                                                        type: 'void',
                                                                        version: '2.0',
                                                                        'x-component': 'Grid',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        'x-initializer': 'details:configureFields',
                                                                        _isJSONSchemaObject: true,
                                                                        'x-uid': 'if73mjxh6vg',
                                                                        'x-async': false,
                                                                        'x-index': 2,
                                                                      },
                                                                      pagination: {
                                                                        type: 'void',
                                                                        version: '2.0',
                                                                        'x-component': 'Pagination',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        _isJSONSchemaObject: true,
                                                                        'x-use-component-props':
                                                                          'useDetailsPaginationProps',
                                                                        'x-uid': 'hmr17xga5tp',
                                                                        'x-async': false,
                                                                        'x-index': 3,
                                                                      },
                                                                    },
                                                                    'x-uid': 'h9zt4k4be9v',
                                                                    'x-async': false,
                                                                    'x-index': 1,
                                                                  },
                                                                },
                                                                'x-uid': 'tlzfekvy3ap',
                                                                'x-async': false,
                                                                'x-index': 1,
                                                              },
                                                            },
                                                            'x-uid': '5p13c577rnh',
                                                            'x-async': false,
                                                            'x-index': 1,
                                                          },
                                                        },
                                                        'x-uid': '8ohkzsbnojh',
                                                        'x-async': false,
                                                        'x-index': 2,
                                                      },
                                                      '2hahll5u63m': {
                                                        type: 'void',
                                                        version: '2.0',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '1.5.0-alpha.4',
                                                        _isJSONSchemaObject: true,
                                                        properties: {
                                                          '9ql9bdn85ds': {
                                                            type: 'void',
                                                            version: '2.0',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '1.5.0-alpha.4',
                                                            _isJSONSchemaObject: true,
                                                            properties: {
                                                              dpop0cdam2d: {
                                                                type: 'void',
                                                                version: '2.0',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:table',
                                                                'x-component': 'CardItem',
                                                                'x-decorator': 'TableBlockProvider',
                                                                'x-acl-action': 'aa.o2m:list',
                                                                'x-app-version': '1.5.0-alpha.4',
                                                                'x-filter-targets': [],
                                                                'x-decorator-props': {
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 20,
                                                                  },
                                                                  rowKey: 'id',
                                                                  dragSort: false,
                                                                  showIndex: true,
                                                                  dataSource: 'main',
                                                                  association: 'aa.o2m',
                                                                },
                                                                _isJSONSchemaObject: true,
                                                                'x-use-decorator-props': 'useTableBlockDecoratorProps',
                                                                properties: {
                                                                  actions: {
                                                                    type: 'void',
                                                                    version: '2.0',
                                                                    'x-component': 'ActionBar',
                                                                    'x-app-version': '1.5.0-alpha.4',
                                                                    'x-initializer': 'table:configureActions',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    _isJSONSchemaObject: true,
                                                                    'x-uid': 'pxm9tputq83',
                                                                    'x-async': false,
                                                                    'x-index': 1,
                                                                  },
                                                                  khpphn7jytt: {
                                                                    type: 'array',
                                                                    version: '2.0',
                                                                    'x-component': 'TableV2',
                                                                    'x-app-version': '1.5.0-alpha.4',
                                                                    'x-initializer': 'table:configureColumns',
                                                                    'x-component-props': {
                                                                      rowKey: 'id',
                                                                      rowSelection: {
                                                                        type: 'checkbox',
                                                                      },
                                                                    },
                                                                    _isJSONSchemaObject: true,
                                                                    'x-use-component-props': 'useTableBlockProps',
                                                                    properties: {
                                                                      actions: {
                                                                        type: 'void',
                                                                        title: '{{ t("Actions") }}',
                                                                        version: '2.0',
                                                                        'x-toolbar': 'TableColumnSchemaToolbar',
                                                                        'x-settings': 'fieldSettings:TableColumn',
                                                                        'x-component': 'TableV2.Column',
                                                                        'x-decorator': 'TableV2.Column.ActionBar',
                                                                        'x-app-version': '1.5.0-alpha.4',
                                                                        'x-initializer': 'table:configureItemActions',
                                                                        'x-action-column': 'actions',
                                                                        'x-toolbar-props': {
                                                                          initializer: 'table:configureItemActions',
                                                                        },
                                                                        _isJSONSchemaObject: true,
                                                                        properties: {
                                                                          fw44ukuqxi0: {
                                                                            type: 'void',
                                                                            version: '2.0',
                                                                            'x-component': 'Space',
                                                                            'x-decorator': 'DndContext',
                                                                            'x-app-version': '1.5.0-alpha.4',
                                                                            'x-component-props': {
                                                                              split: '|',
                                                                            },
                                                                            _isJSONSchemaObject: true,
                                                                            'x-uid': 'hwcjs2lvs7v',
                                                                            'x-async': false,
                                                                            'x-index': 1,
                                                                          },
                                                                        },
                                                                        'x-uid': 'byrh6k4uqa8',
                                                                        'x-async': false,
                                                                        'x-index': 1,
                                                                      },
                                                                    },
                                                                    'x-uid': 'xjjppseblx7',
                                                                    'x-async': false,
                                                                    'x-index': 2,
                                                                  },
                                                                },
                                                                'x-uid': '9qy3a4nvotd',
                                                                'x-async': false,
                                                                'x-index': 1,
                                                              },
                                                            },
                                                            'x-uid': 'fi6rzqcseyb',
                                                            'x-async': false,
                                                            'x-index': 1,
                                                          },
                                                        },
                                                        'x-uid': 'u0mc0xhbeak',
                                                        'x-async': false,
                                                        'x-index': 3,
                                                      },
                                                    },
                                                    'x-uid': 'ed93dypfir7',
                                                    'x-async': false,
                                                    'x-index': 1,
                                                  },
                                                },
                                                'x-uid': 'dbaco5p3izu',
                                                'x-async': false,
                                                'x-index': 1,
                                              },
                                            },
                                            'x-uid': 'cr1dgnksc3q',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'kgareec4xo1',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'rrr1ss36uw1',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '2qqsy80sueg',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'c3hqvxc2ur4',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': '8sfrdie0ufi',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'o20seg4e4sb',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '8mowb0bwiik',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'nb0i8gxqh4a',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'j1c0xypx5mw',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': '5e2ktbtghrf',
    'x-async': true,
    'x-index': 1,
  },
};
