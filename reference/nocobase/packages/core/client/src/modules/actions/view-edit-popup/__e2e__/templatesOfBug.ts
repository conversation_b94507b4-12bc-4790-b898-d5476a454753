/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

import { PageConfig } from '@nocobase/test/e2e';

export const T3910: PageConfig = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '0.21.0-alpha.5',
    properties: {
      g31hdrdqs8h: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '0.21.0-alpha.5',
        properties: {
          row_iz2b4igru45: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 3,
            'x-uid': '617eufdvnh6',
            'x-async': false,
          },
          row_a5ts27jsr0r: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 5,
            'x-uid': '5y6xse8lly4',
            'x-async': false,
          },
          row_7kgysy6ebmv: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 7,
            'x-uid': 'xinsxfgxn6w',
            'x-async': false,
          },
          row_8up6dvn9s9x: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 8,
            properties: {
              srvh9gyv314: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-uid': 'cmfgvfgiu5c',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'xqhlz9kb9cn',
            'x-async': false,
          },
          ellejr0qqmk: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': '332sv3qoo02',
            'x-async': false,
            'x-index': 9,
          },
          iikykifub90: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': 'kwwfi3g1s9c',
            'x-async': false,
            'x-index': 10,
          },
          eocpt7rr5nf: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': 'n8inyj1o337',
            'x-async': false,
            'x-index': 11,
          },
          dmguqyj163z: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            properties: {
              '45z8xeagwh9': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.5',
                properties: {
                  ox51hs3hrtl: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'roles:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'roles',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '0.21.0-alpha.5',
                    properties: {
                      xhstm4sfzvf: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '0.21.0-alpha.5',
                        properties: {
                          sg4oe8v52dv: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '0.21.0-alpha.5',
                            properties: {
                              hhwgvc571rv: {
                                'x-uid': '4xht3tnzsuc',
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                title: '{{ t("Edit") }}',
                                'x-action': 'update',
                                'x-toolbar': 'ActionSchemaToolbar',
                                'x-settings': 'actionSettings:edit',
                                'x-component': 'Action',
                                'x-component-props': {
                                  openMode: 'drawer',
                                  icon: 'EditOutlined',
                                  type: 'primary',
                                },
                                'x-decorator': 'ACLActionProvider',
                                'x-app-version': '0.21.0-alpha.5',
                                'x-linkage-rules': [
                                  {
                                    condition: {
                                      $and: [],
                                    },
                                    actions: [
                                      {
                                        operator: 'disabled',
                                      },
                                    ],
                                  },
                                ],
                                properties: {
                                  drawer: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: '{{ t("Edit record") }}',
                                    'x-component': 'Action.Container',
                                    'x-component-props': {
                                      className: 'nb-action-popup',
                                    },
                                    'x-app-version': '0.21.0-alpha.5',
                                    properties: {
                                      tabs: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Tabs',
                                        'x-component-props': {},
                                        'x-initializer': 'popup:addTab',
                                        'x-app-version': '0.21.0-alpha.5',
                                        properties: {
                                          tab1: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'void',
                                            title: '{{t("Edit")}}',
                                            'x-component': 'Tabs.TabPane',
                                            'x-designer': 'Tabs.Designer',
                                            'x-component-props': {},
                                            'x-app-version': '0.21.0-alpha.5',
                                            properties: {
                                              grid: {
                                                _isJSONSchemaObject: true,
                                                version: '2.0',
                                                type: 'void',
                                                'x-component': 'Grid',
                                                'x-initializer': 'popup:common:addBlock',
                                                'x-app-version': '0.21.0-alpha.5',
                                                'x-uid': 'ahcfvqtca2t',
                                                'x-async': false,
                                                'x-index': 1,
                                              },
                                            },
                                            'x-uid': 'm3qh2qam848',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'qp5j0fo0uz3',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '0jn3kvh5043',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'a0vogjl5s40',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '0.21.0-alpha.5',
                            'x-uid': '088lqkedkmm',
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '0.21.0-alpha.5',
                            'x-uid': 'yg4znwidty0',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'x8clfl5dzzx',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'r91isw3r5hi',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '5dtzmsf8zfj',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'twyfj5klim7',
            'x-async': false,
            'x-index': 12,
          },
        },
        'x-uid': 'gvzvz05oi9h',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'mk589w74bvs',
    'x-async': true,
    'x-index': 1,
  },
};
