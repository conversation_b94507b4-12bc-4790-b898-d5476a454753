/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

import { PageConfig } from '@nocobase/test/e2e';

export const oneFormWithPickerField: PageConfig = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '0.21.0-alpha.5',
    properties: {
      g31hdrdqs8h: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '0.21.0-alpha.5',
        properties: {
          row_iz2b4igru45: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 3,
            'x-uid': '617eufdvnh6',
            'x-async': false,
          },
          row_a5ts27jsr0r: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 5,
            'x-uid': '5y6xse8lly4',
            'x-async': false,
          },
          row_7kgysy6ebmv: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 7,
            'x-uid': 'xinsxfgxn6w',
            'x-async': false,
          },
          row_8up6dvn9s9x: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 8,
            properties: {
              srvh9gyv314: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-uid': 'cmfgvfgiu5c',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'xqhlz9kb9cn',
            'x-async': false,
          },
          ellejr0qqmk: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': '332sv3qoo02',
            'x-async': false,
            'x-index': 9,
          },
          iikykifub90: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': 'kwwfi3g1s9c',
            'x-async': false,
            'x-index': 10,
          },
          eocpt7rr5nf: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            'x-uid': 'n8inyj1o337',
            'x-async': false,
            'x-index': 11,
          },
          '00va5gmpktn': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.5',
            properties: {
              uhw2kshmg5n: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.5',
                properties: {
                  n3o7bqxxmdp: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action-props': {
                      skipScopeCheck: true,
                    },
                    'x-acl-action': 'users:create',
                    'x-decorator': 'FormBlockProvider',
                    'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:createForm',
                    'x-component': 'CardItem',
                    'x-app-version': '0.21.0-alpha.5',
                    properties: {
                      ab6zxnga5yh: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useCreateFormBlockProps',
                        'x-app-version': '0.21.0-alpha.5',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'form:configureFields',
                            'x-app-version': '0.21.0-alpha.5',
                            properties: {
                              fv88xejydf7: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '0.21.0-alpha.5',
                                properties: {
                                  kgc72n2dnjs: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '0.21.0-alpha.5',
                                    properties: {
                                      roles: {
                                        'x-uid': 'xh41kv06w9h',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.roles',
                                        'x-component-props': {
                                          fieldNames: {
                                            label: 'name',
                                            value: 'name',
                                          },
                                          mode: 'Picker',
                                        },
                                        'x-app-version': '0.21.0-alpha.5',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'j9i44yv2zv0',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'prexj7926ya',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'emwe7dhqqxc',
                            'x-async': false,
                            'x-index': 1,
                          },
                          ypc6lolh1t5: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'createForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                marginTop: 24,
                              },
                            },
                            'x-app-version': '0.21.0-alpha.5',
                            'x-uid': 'hurfo4v8c9d',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'rs8dkg2723x',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 't03by8fpct5',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'bempwzkuh75',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '6dl0rbnvxxx',
            'x-async': false,
            'x-index': 12,
          },
        },
        'x-uid': 'gvzvz05oi9h',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'mk589w74bvs',
    'x-async': true,
    'x-index': 1,
  },
};
