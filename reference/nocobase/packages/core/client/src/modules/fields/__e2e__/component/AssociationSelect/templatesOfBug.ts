/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const associationSelectDataScope = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.0.0-alpha.17',
    properties: {
      i9pflzjyioi: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.0.0-alpha.17',
        properties: {
          '4ai4zgcz8ee': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.17',
            properties: {
              vpldrh6hgmf: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.17',
                properties: {
                  '5ieufmio6lw': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action-props': {
                      skipScopeCheck: true,
                    },
                    'x-acl-action': 'student:create',
                    'x-decorator': 'FormBlockProvider',
                    'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'student',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:createForm',
                    'x-component': 'CardItem',
                    'x-app-version': '1.0.0-alpha.17',
                    properties: {
                      '5husg9t3vv9': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useCreateFormBlockProps',
                        'x-app-version': '1.0.0-alpha.17',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'form:configureFields',
                            'x-app-version': '1.0.0-alpha.17',
                            properties: {
                              tl52qwh5qb2: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.17',
                                properties: {
                                  k0bea3ifdvt: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.17',
                                    properties: {
                                      school: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'student.school',
                                        'x-component-props': {
                                          fieldNames: {
                                            value: 'id',
                                            label: 'id',
                                          },
                                        },
                                        'x-app-version': '1.0.0-alpha.17',
                                        'x-uid': '4aqvcj92dmg',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '3menjy0e1ej',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'oowsi97eacq',
                                'x-async': false,
                                'x-index': 1,
                              },
                              whxgd4trw89: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.17',
                                properties: {
                                  '2lbxox3r15o': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.17',
                                    properties: {
                                      class: {
                                        'x-uid': '1gwunmk7b3c',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'student.class',
                                        'x-component-props': {
                                          fieldNames: {
                                            value: 'id',
                                            label: 'id',
                                          },
                                          service: {
                                            params: {
                                              filter: {
                                                $and: [
                                                  {
                                                    school: {
                                                      id: {
                                                        $eq: '{{$nForm.school.id}}',
                                                      },
                                                    },
                                                  },
                                                ],
                                              },
                                            },
                                          },
                                        },
                                        'x-app-version': '1.0.0-alpha.17',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'ntz289r21df',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'cavrlbod03e',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'k7qwqqhdx60',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '1kqjiuyj895': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'createForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                marginTop: 'var(--nb-spacing)',
                              },
                            },
                            'x-app-version': '1.0.0-alpha.17',
                            'x-uid': 'cwurx7plt95',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': '306cgan6w47',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'wy8gkqcnr6b',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'd30aarrzirt',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'pqdwwjn7y28',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'u3r6l5ed5c8',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': '42t4voitjmc',
    'x-async': true,
    'x-index': 1,
  },
  collections: [
    {
      name: 'school',
      fields: [
        {
          key: 'ze26tawdlux',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'school',
          parentKey: null,
          reverseKey: null,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
      ],
      filterTargetKey: 'id',
    },
    {
      name: 'class',
      fields: [
        {
          key: 'rkb2b156str',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'class',
          parentKey: null,
          reverseKey: null,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: 's804a0a6hb4',
          name: 'f_nwk9mip9y1k',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'class',
          parentKey: null,
          reverseKey: null,
          isForeignKey: true,
          uiSchema: {
            type: 'number',
            title: 'f_nwk9mip9y1k',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: 'btl687vmwyo',
          name: 'f_ih087vrmag4',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'class',
          parentKey: null,
          reverseKey: null,
          isForeignKey: true,
          uiSchema: {
            type: 'number',
            title: 'f_ih087vrmag4',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: 'a35wf3880bb',
          name: 'f_uwcl0rf78mn',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'class',
          parentKey: null,
          reverseKey: null,
          isForeignKey: true,
          uiSchema: {
            type: 'number',
            title: 'f_uwcl0rf78mn',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },

        {
          key: 'nt7i4vmih24',
          name: 'school',
          type: 'belongsTo',
          interface: 'm2o',
          description: null,
          collectionName: 'class',
          parentKey: null,
          reverseKey: null,
          foreignKey: 'f_uwcl0rf78mn',
          onDelete: 'SET NULL',
          uiSchema: {
            'x-component': 'AssociationField',
            'x-component-props': {
              multiple: false,
            },
            title: 'school',
          },
          target: 'school',
          targetKey: 'id',
        },
      ],
      filterTargetKey: 'id',
    },
    {
      name: 'student',

      fields: [
        {
          key: 'pjz61nq67ym',
          name: 'id',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'student',
          parentKey: null,
          reverseKey: null,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
          uiSchema: {
            type: 'number',
            title: '{{t("ID")}}',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: 'jrt23ehbhwn',
          name: 'f_firb6d8f8jq',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'student',
          parentKey: null,
          reverseKey: null,
          isForeignKey: true,
          uiSchema: {
            type: 'number',
            title: 'f_firb6d8f8jq',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: '9hydd4b1out',
          name: 'f_7kxab0celw3',
          type: 'bigInt',
          interface: 'integer',
          description: null,
          collectionName: 'student',
          parentKey: null,
          reverseKey: null,
          isForeignKey: true,
          uiSchema: {
            type: 'number',
            title: 'f_7kxab0celw3',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          key: 'xwpw9a9f7y6',
          name: 'class',
          type: 'belongsTo',
          interface: 'm2o',
          description: null,
          collectionName: 'student',
          parentKey: null,
          reverseKey: null,
          foreignKey: 'f_firb6d8f8jq',
          onDelete: 'SET NULL',
          uiSchema: {
            'x-component': 'AssociationField',
            'x-component-props': {
              multiple: false,
            },
            title: 'class',
          },
          target: 'class',
          targetKey: 'id',
        },
        {
          key: 'by6lqjkl50g',
          name: 'school',
          type: 'belongsTo',
          interface: 'm2o',
          description: null,
          collectionName: 'student',
          parentKey: null,
          reverseKey: null,
          foreignKey: 'f_7kxab0celw3',
          onDelete: 'SET NULL',
          uiSchema: {
            'x-component': 'AssociationField',
            'x-component-props': {
              multiple: false,
            },
            title: 'school',
          },
          target: 'school',
          targetKey: 'id',
        },
      ],

      filterTargetKey: 'id',
    },
  ],
};
export const usingMultiLevelAssociationFieldValuesInDataScope = {
  collections: [
    {
      name: 'test',
      fields: [
        {
          name: 'a',
          interface: 'm2m',
          target: 'A',
        },
        {
          name: 'b',
          interface: 'm2m',
          target: 'B',
        },
      ],
    },
    {
      name: 'A',
      fields: [
        {
          name: 'b',
          interface: 'm2m',
          target: 'B',
        },
      ],
    },
    {
      name: 'B',
    },
  ],
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      dvf2mwe6bvn: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          '98y2p0ausvb': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.8',
            properties: {
              t865qbsqxhb: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.8',
                properties: {
                  '4ceh9f8smkv': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action-props': {
                      skipScopeCheck: true,
                    },
                    'x-acl-action': 'test:create',
                    'x-decorator': 'FormBlockProvider',
                    'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'test',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:createForm',
                    'x-component': 'CardItem',
                    'x-app-version': '1.4.8',
                    properties: {
                      dq224dw86wj: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useCreateFormBlockProps',
                        'x-app-version': '1.4.8',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'form:configureFields',
                            'x-app-version': '1.4.8',
                            properties: {
                              y6j67vnmhw4: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.8',
                                properties: {
                                  bef7a7oph86: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.8',
                                    properties: {
                                      a: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'test.a',
                                        'x-component-props': {
                                          fieldNames: {
                                            label: 'id',
                                            value: 'id',
                                          },
                                        },
                                        'x-app-version': '1.4.8',
                                        'x-uid': 'y3gtqj8pdqb',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'xad7ytjjufv',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '4gj0r5upoq0',
                                'x-async': false,
                                'x-index': 1,
                              },
                              shmpsuanpwa: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.8',
                                properties: {
                                  uzy27mws6no: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.8',
                                    properties: {
                                      b: {
                                        'x-uid': '7h0yrs8u2bl',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'test.b',
                                        'x-component-props': {
                                          fieldNames: {
                                            label: 'id',
                                            value: 'id',
                                          },
                                          service: {
                                            params: {
                                              filter: {
                                                $and: [
                                                  {
                                                    id: {
                                                      $eq: '{{$nForm.a.b.id}}',
                                                    },
                                                  },
                                                ],
                                              },
                                            },
                                          },
                                        },
                                        'x-app-version': '1.4.8',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'by3u5cxvah1',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'gdn131j2208',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '9navauip2qh',
                            'x-async': false,
                            'x-index': 1,
                          },
                          culdrszlt2z: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'createForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                            },
                            'x-app-version': '1.4.8',
                            'x-uid': 'l8uhg0tzxdh',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'b4m5no3h8xp',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'a0lixg1oxfr',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'nrbzm8f8wp9',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'co3ws9xtbww',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'ybhswoe7nee',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'd5c5o9pl70o',
    'x-async': true,
    'x-index': 1,
  },
};
