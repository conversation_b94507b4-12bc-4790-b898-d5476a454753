/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

// https://github.com/nocobase/nocobase/pull/4559
export const oneFormAndOneTable = {
  collections: [
    {
      name: 'general',
      fields: [
        {
          interface: 'url',
          name: 'url',
        },
      ],
    },
  ],
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-index': 1,
    properties: {
      '4nbajnebmlr': {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-index': 1,
        properties: {
          cf8uh9g697n: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.17',
            'x-index': 1,
            properties: {
              '1japwy7nj0k': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.17',
                'x-index': 1,
                properties: {
                  ahp6bbaj61t: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action-props': {
                      skipScopeCheck: true,
                    },
                    'x-acl-action': 'general:create',
                    'x-decorator': 'FormBlockProvider',
                    'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'general',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:createForm',
                    'x-component': 'CardItem',
                    'x-app-version': '1.0.0-alpha.17',
                    'x-index': 1,
                    properties: {
                      xgf2jpb0mwf: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useCreateFormBlockProps',
                        'x-app-version': '1.0.0-alpha.17',
                        'x-index': 1,
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'form:configureFields',
                            'x-app-version': '1.0.0-alpha.17',
                            'x-index': 1,
                            properties: {
                              yypv0ey12iv: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.17',
                                'x-index': 1,
                                properties: {
                                  '1qgv0svx6i0': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.17',
                                    'x-index': 1,
                                    properties: {
                                      url: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'general.url',
                                        'x-component-props': {
                                          component: 'Input.URL',
                                        },
                                        'x-app-version': '1.0.0-alpha.17',
                                        'x-index': 1,
                                        'x-uid': '3d99xtkyxa7',
                                        'x-async': false,
                                      },
                                    },
                                    'x-uid': '4qa57usgxfr',
                                    'x-async': false,
                                  },
                                },
                                'x-uid': 'c4wm0124cvk',
                                'x-async': false,
                              },
                            },
                            'x-uid': 'heykz2e3s19',
                            'x-async': false,
                          },
                          '0tquv59oojr': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'createForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                marginTop: 'var(--nb-spacing)',
                              },
                            },
                            'x-app-version': '1.0.0-alpha.17',
                            'x-index': 2,
                            properties: {
                              f8subamq3yq: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Submit") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useCreateActionProps',
                                'x-toolbar': 'ActionSchemaToolbar',
                                'x-settings': 'actionSettings:createSubmit',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {
                                  triggerWorkflows: [],
                                },
                                type: 'void',
                                'x-app-version': '1.0.0-alpha.17',
                                'x-index': 1,
                                'x-uid': 'j1h5ugxq7nq',
                                'x-async': false,
                              },
                            },
                            'x-uid': '0r2mv62cvio',
                            'x-async': false,
                          },
                        },
                        'x-uid': 'em2t2fhaiw6',
                        'x-async': false,
                      },
                    },
                    'x-uid': 'tpwwr4b5p54',
                    'x-async': false,
                  },
                },
                'x-uid': 'aq7yxapodnw',
                'x-async': false,
              },
            },
            'x-uid': '1utnt4iy7th',
            'x-async': false,
          },
          dqg82cp57e1: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.17',
            'x-index': 2,
            properties: {
              gqlczodkz2b: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.17',
                'x-index': 1,
                properties: {
                  dlrpbn2z0zb: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'general:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'general',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.0.0-alpha.17',
                    'x-index': 1,
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.0.0-alpha.17',
                        'x-index': 1,
                        properties: {
                          h38nqwjf1zw: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            title: '{{ t("Refresh") }}',
                            'x-action': 'refresh',
                            'x-component': 'Action',
                            'x-use-component-props': 'useRefreshActionProps',
                            'x-toolbar': 'ActionSchemaToolbar',
                            'x-settings': 'actionSettings:refresh',
                            'x-component-props': {
                              icon: 'ReloadOutlined',
                            },
                            'x-align': 'right',
                            type: 'void',
                            'x-app-version': '1.0.0-alpha.17',
                            'x-uid': '3ym5qa392m0',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'c6tw6pehj86',
                        'x-async': false,
                      },
                      tvde4dndsqj: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.0.0-alpha.17',
                        'x-index': 2,
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.0.0-alpha.17',
                            'x-index': 1,
                            properties: {
                              wptjn8efkdq: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.0.0-alpha.17',
                                'x-index': 1,
                                'x-uid': 'x89784q83t2',
                                'x-async': false,
                              },
                            },
                            'x-uid': '1yzn9sblzep',
                            'x-async': false,
                          },
                          raxcojsopou: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.0-alpha.17',
                            'x-index': 2,
                            properties: {
                              url: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'general.url',
                                'x-component': 'CollectionField',
                                'x-component-props': {},
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.0-alpha.17',
                                'x-index': 1,
                                'x-uid': 'qni0na740t1',
                                'x-async': false,
                              },
                            },
                            'x-uid': '5lq7abdz67x',
                            'x-async': false,
                          },
                        },
                        'x-uid': 'zc0cntdtdr4',
                        'x-async': false,
                      },
                    },
                    'x-uid': 'hbmp3k5e1i3',
                    'x-async': false,
                  },
                },
                'x-uid': 'jbxa0cnjm1w',
                'x-async': false,
              },
            },
            'x-uid': 'yqvfdsci5v8',
            'x-async': false,
          },
        },
        'x-uid': 'a1iy1sw0sus',
        'x-async': false,
      },
    },
    'x-uid': 'v3zv08v0hwn',
    'x-async': true,
  },
};
export const ellipsis = {
  collections: [
    {
      name: 'testEllipsis',
      fields: [
        {
          name: 'input',
          interface: 'input',
        },
        {
          name: 'inputURL',
          interface: 'url',
        },
        {
          name: 'inputTextArea',
          interface: 'textarea',
        },
        {
          name: 'inputJSON',
          interface: 'json',
        },
        {
          name: 'richText',
          interface: 'richText',
        },
        {
          name: 'markdown',
          interface: 'markdown',
        },
        {
          name: 'type',
          interface: 'select',
          uiSchema: {
            enum: [
              {
                value: '1',
                label: 'type1',
              },
              {
                value: '2',
                label: 'type2',
              },
              {
                value: '3',
                label: 'type3',
              },
            ],
            type: 'string',
            'x-component': 'Select',
            title: 'Text type',
          },
        },
        {
          name: 'sort',
          interface: 'sort',
          scopeKey: 'type',
        },
        // no vditor plugin in E2E
        // {
        //   name: 'markdownVditor',
        //   interface: 'vditor',
        // },
      ],
    },
  ],
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      g965qvtvtmj: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          '47yh43cnlx6': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.0-alpha',
            properties: {
              d3lptktsh4f: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.0-alpha',
                properties: {
                  rscvdeyaju5: {
                    'x-uid': 'trvwcgwqrne',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'testEllipsis:list',
                    'x-decorator': 'KanbanBlockProvider',
                    'x-decorator-props': {
                      collection: 'testEllipsis',
                      dataSource: 'main',
                      action: 'list',
                      groupField: 'type',
                      sortField: 'sort',
                      params: {
                        paginate: false,
                        sort: ['sort'],
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:kanban',
                    'x-component': 'CardItem',
                    'x-app-version': '1.4.0-alpha',
                    'x-component-props': {
                      title: 'Kanban',
                    },
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'kanban:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.4.0-alpha',
                        'x-uid': 'sqcr02yymd5',
                        'x-async': false,
                        'x-index': 1,
                      },
                      gzcs432mvah: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'Kanban',
                        'x-use-component-props': 'useKanbanBlockProps',
                        'x-app-version': '1.4.0-alpha',
                        properties: {
                          card: {
                            'x-uid': 'tfgo9j7lsov',
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-read-pretty': true,
                            'x-label-disabled': true,
                            'x-decorator': 'BlockItem',
                            'x-component': 'Kanban.Card',
                            'x-component-props': {
                              openMode: 'drawer',
                            },
                            'x-designer': 'Kanban.Card.Designer',
                            'x-app-version': '1.4.0-alpha',
                            'x-action-context': {
                              dataSource: 'main',
                              collection: 'testEllipsis',
                            },
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-component-props': {
                                  dndContext: false,
                                },
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  j74fxk1mii4: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      r3yoqm2t6sw: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          input: {
                                            'x-uid': 'cmtoq1tlgdd',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.input',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'lzbi2cq9y83',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'odaqjgm1vrr',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                  '68ssogjqxmm': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      b4720r6gtvv: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          inputURL: {
                                            'x-uid': '5iouqnm9ad9',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputURL',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'dhejhsb1she',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'oniopbss0x6',
                                    'x-async': false,
                                    'x-index': 2,
                                  },
                                  g9jo9l3iazi: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      rr5i7shb90r: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          inputTextArea: {
                                            'x-uid': 'hg85l2u35fm',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputTextArea',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'p11om0d81vk',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'q6ejh1x8nbr',
                                    'x-async': false,
                                    'x-index': 3,
                                  },
                                  '9qragbojca7': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      '64iwk8759di': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          inputJSON: {
                                            'x-uid': '9yq6010lb9x',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputJSON',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'ok3ch83z9q1',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'mrr6rhu2aua',
                                    'x-async': false,
                                    'x-index': 4,
                                  },
                                  kg1e2nql18g: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      cfmae8pdbr9: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          richText: {
                                            'x-uid': '9b6esxdq60r',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.richText',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'a0u0o2i8wy6',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'mx2k9u8tg7g',
                                    'x-async': false,
                                    'x-index': 5,
                                  },
                                  qk4i0u51ih9: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    properties: {
                                      phusp5bo3yf: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        properties: {
                                          markdown: {
                                            'x-uid': 'i610e8gvexu',
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.markdown',
                                            'x-component-props': {
                                              ellipsis: false,
                                            },
                                            'x-read-pretty': true,
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'j9kcoshwqcz',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'jntewcvl8kp',
                                    'x-async': false,
                                    'x-index': 6,
                                  },
                                },
                                'x-uid': 'g0iysqk2ap4',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-async': false,
                            'x-index': 1,
                          },
                          cardViewer: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("View") }}',
                            'x-designer': 'Action.Designer',
                            'x-component': 'Kanban.CardViewer',
                            'x-action': 'view',
                            'x-component-props': {
                              openMode: 'drawer',
                            },
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              drawer: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                title: '{{ t("View record") }}',
                                'x-component': 'Action.Container',
                                'x-component-props': {
                                  className: 'nb-action-popup',
                                },
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  tabs: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Tabs',
                                    'x-component-props': {},
                                    'x-initializer': 'popup:addTab',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      tab1: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        title: '{{t("Details")}}',
                                        'x-component': 'Tabs.TabPane',
                                        'x-designer': 'Tabs.Designer',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          grid: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'void',
                                            'x-component': 'Grid',
                                            'x-initializer': 'popup:common:addBlock',
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': '3c7oi86zmc1',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'pqclgxhsiny',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'x4a9lybp1x8',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'ny00k7fdxcc',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'jdsdt7u9du6',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'wvx00mxj9g6',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'c0f710dofpg',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '1w93tt24e5j',
            'x-async': false,
            'x-index': 1,
          },
          aga7m08a6c5: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.0-alpha',
            properties: {
              urulcx1gmb7: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.0-alpha',
                properties: {
                  pz8btjnnmso: {
                    'x-uid': 'h8gqtuvahgr',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'testEllipsis:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'testEllipsis',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.4.0-alpha',
                    'x-component-props': {
                      title: 'Table',
                    },
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.4.0-alpha',
                        'x-uid': 'izgz249whns',
                        'x-async': false,
                        'x-index': 1,
                      },
                      mn9nlj3ow1o: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.4.0-alpha',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              '96qmif5wzzi': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'pk4d8ksx6x0',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 't4n99un60td',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '1oe2pn371kt': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              input: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.input',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'ehv6e4f31y1',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'kujg1rf3yoy',
                            'x-async': false,
                            'x-index': 2,
                          },
                          b0f7n4ot0uf: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              inputURL: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.inputURL',
                                'x-component': 'CollectionField',
                                'x-component-props': {},
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'c30nzrikd4q',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'vp9hay0y3xd',
                            'x-async': false,
                            'x-index': 3,
                          },
                          '9ciu8x0w6m2': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              inputTextArea: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.inputTextArea',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'ua7s6vv4js2',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'swksvx7kdqk',
                            'x-async': false,
                            'x-index': 4,
                          },
                          vqzoy6wh3v9: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              inputJSON: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.inputJSON',
                                'x-component': 'CollectionField',
                                'x-component-props': {},
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'qkx9xmrpqtr',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '4accak7p0qv',
                            'x-async': false,
                            'x-index': 5,
                          },
                          '85j1ll3q04j': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              richText: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.richText',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': '9ofnczj2lvz',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '63zulw34tn4',
                            'x-async': false,
                            'x-index': 6,
                          },
                          kshui6i56xv: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              markdown: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'testEllipsis.markdown',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'et6l8blhhvd',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'hplvy9e9kli',
                            'x-async': false,
                            'x-index': 7,
                          },
                        },
                        'x-uid': 'tsj4d14zx8s',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'q3jchapkn19',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '8d8ganq4ooq',
            'x-async': false,
            'x-index': 2,
          },
          gc9ffz397dj: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.0-alpha',
            properties: {
              lwvmogv3iub: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.0-alpha',
                properties: {
                  zomdsijnphc: {
                    'x-uid': '3mmm519tj8y',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'testEllipsis:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'testEllipsis',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '1.4.0-alpha',
                    'x-component-props': {
                      title: 'Details',
                    },
                    properties: {
                      q4sxbegbfrc: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '1.4.0-alpha',
                        properties: {
                          hjwsp9e5tel: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '1.4.0-alpha',
                            'x-uid': 'atz6i01mjx1',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              '92sx6204v0g': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  iuy9o1gjepl: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      input: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.input',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': 'q9fpbjwdira',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'mltsb815ojg',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '9xvn79znd7e',
                                'x-async': false,
                                'x-index': 1,
                              },
                              su4uh3mv5tr: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  z7nuq3tt62h: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      inputURL: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.inputURL',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': 'k90a3p14af2',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '5dcjcop0lgk',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'h3nib7nzhxr',
                                'x-async': false,
                                'x-index': 2,
                              },
                              rpvszdqdlyo: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  xvavsr4dpuv: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      inputTextArea: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.inputTextArea',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': '53ynbm1dfae',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'lu8ngjdigry',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '761mge52ckk',
                                'x-async': false,
                                'x-index': 3,
                              },
                              u119pc69wdu: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  '71il5i0uwj1': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      inputJSON: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.inputJSON',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': 'zled414bjdg',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'oigd8yqkr6z',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'mzos557de1m',
                                'x-async': false,
                                'x-index': 4,
                              },
                              iazxy8avs8h: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  '2qxnd682jcw': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      richText: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.richText',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': 'wrzndo8aqjt',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '9go4enrcgte',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'f373k9h33kp',
                                'x-async': false,
                                'x-index': 5,
                              },
                              '0i4jsug4icz': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  gub9cdiv9m5: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      markdown: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'testEllipsis.markdown',
                                        'x-component-props': {},
                                        'x-app-version': '1.4.0-alpha',
                                        'x-uid': 'twvvwp38n03',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '5ckll512oq5',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'x1e55qw6vng',
                                'x-async': false,
                                'x-index': 6,
                              },
                            },
                            'x-uid': 'nivnp8d6ja5',
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '1.4.0-alpha',
                            'x-uid': 't33e0npeqng',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'ek2ocsznoa8',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'zcihllchlwv',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'taj8od92vlk',
            'x-async': false,
            'x-index': 3,
          },
          '6wr9h8rtsps': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.0-alpha',
            properties: {
              '4unxqlrawlv': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.0-alpha',
                properties: {
                  '1nexmi69n86': {
                    'x-uid': '302sznn4xja',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'testEllipsis:view',
                    'x-decorator': 'List.Decorator',
                    'x-use-decorator-props': 'useListBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'testEllipsis',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 10,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'CardItem',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:list',
                    'x-app-version': '1.4.0-alpha',
                    'x-component-props': {
                      title: 'List',
                    },
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'list:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.4.0-alpha',
                        'x-uid': '1sv29ingnfm',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'List',
                        'x-use-component-props': 'useListBlockProps',
                        'x-app-version': '1.4.0-alpha',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'List.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useListItemProps',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  frmr83bjszl: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      '2dl26hscje6': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          input: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.input',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'uny79lee2lp',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'cgdnw331nj2',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'uj6esc5yiqi',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                  zzv7dje76hr: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      '1i326ws5mtd': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputURL: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputURL',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'tbraiag1qzf',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': '02347u0s0ss',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'v9jwkbjhn15',
                                    'x-async': false,
                                    'x-index': 2,
                                  },
                                  qfcotp79sqd: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      d4lp4f8z8s4: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputTextArea: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputTextArea',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'qt5rwnbzo72',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'i50wpr0134t',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'uyf89ww8fxp',
                                    'x-async': false,
                                    'x-index': 3,
                                  },
                                  '2dxqgysh2lz': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      l7nofmkyct0: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputJSON: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputJSON',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'gnhcv2evznj',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'fxym8vkrtf5',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '2cvkfh2mfhm',
                                    'x-async': false,
                                    'x-index': 4,
                                  },
                                  e5rl5jcyha6: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      m0cnws87pj0: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          richText: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.richText',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': '8e81mjvpmd7',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'z96bi1of3oo',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '96tmezq68bl',
                                    'x-async': false,
                                    'x-index': 5,
                                  },
                                  l9npx8a01sa: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      '0t70ay46j5b': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          markdown: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.markdown',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'hjcpn1qhvog',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'uwl0bvglk4d',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'll64su7i3do',
                                    'x-async': false,
                                    'x-index': 6,
                                  },
                                },
                                'x-uid': '456jc3ohfgk',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'list:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useListActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'n8p6b2qcsn9',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'ukw31qvotuj',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'l68f49rnjmy',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '36t6bxu86ub',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'hg45dt831lp',
            'x-async': false,
            'x-index': 4,
          },
          '2yi1elrltsf': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.4.0-alpha',
            properties: {
              '9ptvkwrgc57': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.4.0-alpha',
                properties: {
                  '20ukde66ghk': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'testEllipsis:view',
                    'x-decorator': 'GridCard.Decorator',
                    'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'testEllipsis',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 12,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'BlockItem',
                    'x-use-component-props': 'useGridCardBlockItemProps',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:gridCard',
                    'x-app-version': '1.4.0-alpha',
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'gridCard:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.4.0-alpha',
                        'x-uid': 'wtt4c62i90w',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'GridCard',
                        'x-use-component-props': 'useGridCardBlockProps',
                        'x-app-version': '1.4.0-alpha',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'GridCard.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useGridCardItemProps',
                            'x-app-version': '1.4.0-alpha',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '1.4.0-alpha',
                                properties: {
                                  gqzlaba1um3: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      rqev67avkcg: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          input: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.input',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'qdsrg1nhuyv',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'no89ehqn0p5',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '3borzpb9spq',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                  vabc618iob9: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      x200si2lp2o: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputURL: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputURL',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': '6jvpbc5arkv',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'qu7kx2x4cqn',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'oo6ie8c38k2',
                                    'x-async': false,
                                    'x-index': 2,
                                  },
                                  hzg1lcj809i: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      kboo5qirkn7: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputTextArea: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputTextArea',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'sgnvmbuzy25',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': '4zd1fg1qu5o',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'wolac9gfhwt',
                                    'x-async': false,
                                    'x-index': 3,
                                  },
                                  j6p2fykaqxd: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      t4ehboas9ma: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          inputJSON: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.inputJSON',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'yos6wc0ouvj',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'houb8q3dc2f',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'hwtmz9xtvi0',
                                    'x-async': false,
                                    'x-index': 4,
                                  },
                                  '4lfggynjp26': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      ggqr2mg8wym: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          richText: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.richText',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'd9d9ek039au',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'imt0f9z7tcj',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '9kb6nqrq0nc',
                                    'x-async': false,
                                    'x-index': 5,
                                  },
                                  '4j7sg3potav': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '1.4.0-alpha',
                                    properties: {
                                      w9j02mmgzro: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '1.4.0-alpha',
                                        properties: {
                                          markdown: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'testEllipsis.markdown',
                                            'x-component-props': {},
                                            'x-app-version': '1.4.0-alpha',
                                            'x-uid': 'ff64l0hqaen',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'aqwvp7swn3e',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '4i16nph2r2b',
                                    'x-async': false,
                                    'x-index': 6,
                                  },
                                },
                                'x-uid': 'k3ndudpzcp5',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'gridCard:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useGridCardActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '1.4.0-alpha',
                                'x-uid': 'iapjbqie0ni',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'zuvfss2wgq6',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'u4b8delxyvw',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'c4yjem1mj32',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'bla6ddipf6e',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'vvfih1g0q7n',
            'x-async': false,
            'x-index': 5,
          },
        },
        'x-uid': '47ks7rghprw',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'g77bu4dcydq',
    'x-async': true,
    'x-index': 1,
  },
};
export const shouldImmediatelyShowDrawerWhenClickingEnableLinkForTheFirstTime = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.6.0-alpha.2',
    properties: {
      j5rf9qdpszc: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.6.0-alpha.2',
        properties: {
          '9l6vdnwqbbj': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.6.0-alpha.2',
            properties: {
              '73wtbtpyh75': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.6.0-alpha.2',
                properties: {
                  ifglrmm4dfm: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.6.0-alpha.2',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.6.0-alpha.2',
                        'x-uid': 'vst95b5x5bc',
                        'x-async': false,
                        'x-index': 1,
                      },
                      pj31onfd6q8: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.6.0-alpha.2',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.6.0-alpha.2',
                            properties: {
                              '5w0g8630wox': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.6.0-alpha.2',
                                'x-uid': '42m6iu48l85',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'vrzc3es2o9a',
                            'x-async': false,
                            'x-index': 1,
                          },
                          dxeciedcefm: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.6.0-alpha.2',
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.6.0-alpha.2',
                                'x-uid': '11mfs3io56j',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'e49v8cnkpcp',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'w0r6vc8ubhp',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'oimaujl994x',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '55a6opby9jt',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '8zdq1dq4bfd',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': '1c51z6cu5u2',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'u3njequ14zz',
    'x-async': true,
    'x-index': 1,
  },
};

export const oneDetailBlockFieldWidthFieldStyle = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': 'v1.7.0-beta.5',
    properties: {
      j5rf9qdpszc: {
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          x72noji3ol7: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.7.0-beta.5',
            properties: {
              '6klgaiwxs8q': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.7.0-beta.5',
                properties: {
                  dpj7gevoiqw: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '1.7.0-beta.5',
                    properties: {
                      '5zo8ay6xu74': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '1.7.0-beta.5',
                        properties: {
                          '2u7t8s8zffc': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '1.7.0-beta.5',
                            'x-uid': 'ajfbilj1eas',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '1.7.0-beta.5',
                            properties: {
                              g1jwsjkiks0: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.7.0-beta.5',
                                properties: {
                                  gi5z3ss61t2: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.7.0-beta.5',
                                    properties: {
                                      nickname: {
                                        'x-uid': '2a4om1os4i8',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '1.7.0-beta.5',
                                        // 'x-linkage-style-rules': [
                                        //   {
                                        //     condition: {
                                        //       $and: [],
                                        //     },
                                        //     actions: [
                                        //       {
                                        //         operator: 'fontSize',
                                        //         value: {
                                        //           mode: 'constant',
                                        //           value: 40,
                                        //         },
                                        //       },
                                        //     ],
                                        //   },
                                        // ],
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'ki6424bbnoa',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '387m3183ea3',
                                'x-async': false,
                                'x-index': 1,
                              },
                              '1rhnbs2gcca': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.7.0-beta.5',
                                properties: {
                                  d5ma1xl6j2i: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.7.0-beta.5',
                                    properties: {
                                      username: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.username',
                                        'x-component-props': {},
                                        'x-app-version': '1.7.0-beta.5',
                                        'x-uid': '1ayny4b2ylm',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'cdkqplw5ec7',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'k35rh0ef681',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'erulf9efwvv',
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '1.7.0-beta.5',
                            'x-uid': 'rcmvkgxvob9',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'jra1fx1jp0k',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': '3u4qbg33yhg',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'g126n7qmm1k',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'ftxf3gxjwxs',
            'x-async': false,
            'x-index': 1,
          },
        },
        name: 'j8dv3zpp2l7',
        'x-uid': 'n9tpyozmuz9',
        'x-async': true,
        'x-index': 1,
      },
    },
    'x-uid': 'u3njequ14zz',
    'x-async': true,
    'x-index': 1,
  },
};
