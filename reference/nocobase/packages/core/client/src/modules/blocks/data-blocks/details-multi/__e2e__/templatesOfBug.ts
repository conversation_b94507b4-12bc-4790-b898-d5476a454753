/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const oneEmptyTableWithUsers = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      zvj8cbqvt05: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          j0zzcf3k2vc: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.6',
            properties: {
              pn0pgjxjlz2: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.6',
                properties: {
                  o9zor60xpvi: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '0.21.0-alpha.6',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.6',
                        'x-uid': 'dnnfz8xyqh9',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '3rr559rnt6k': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.6',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-designer': 'TableV2.ActionColumnDesigner',
                            'x-initializer': 'table:configureItemActions',
                            'x-app-version': '0.21.0-alpha.6',
                            properties: {
                              cf7dj1iffh3: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '0.21.0-alpha.6',
                                properties: {
                                  oealnj6s2rw: {
                                    'x-uid': 'e32p6hfhh35',
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: 'View record',
                                    'x-action': 'view',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:view',
                                    'x-component': 'Action.Link',
                                    'x-component-props': {
                                      openMode: 'drawer',
                                      danger: false,
                                    },
                                    'x-decorator': 'ACLActionProvider',
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    properties: {
                                      drawer: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        title: '{{ t("View record") }}',
                                        'x-component': 'Action.Container',
                                        'x-component-props': {
                                          className: 'nb-action-popup',
                                        },
                                        properties: {
                                          tabs: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'void',
                                            'x-component': 'Tabs',
                                            'x-component-props': {},
                                            'x-initializer': 'popup:addTab',
                                            properties: {
                                              tab1: {
                                                _isJSONSchemaObject: true,
                                                version: '2.0',
                                                type: 'void',
                                                title: '{{t("Details")}}',
                                                'x-component': 'Tabs.TabPane',
                                                'x-designer': 'Tabs.Designer',
                                                'x-component-props': {},
                                                properties: {
                                                  grid: {
                                                    _isJSONSchemaObject: true,
                                                    version: '2.0',
                                                    type: 'void',
                                                    'x-component': 'Grid',
                                                    'x-initializer': 'popup:common:addBlock',
                                                    'x-uid': 'iuh8edqjzjf',
                                                    'x-async': false,
                                                    'x-index': 1,
                                                  },
                                                },
                                                'x-uid': 'c7s6mbv0ifc',
                                                'x-async': false,
                                                'x-index': 1,
                                              },
                                            },
                                            'x-uid': 'l7mf3lk2rfi',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': '96mjki0iss6',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '4wz5e2y5mi3',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'jursnceu1wj',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'zysxygy87b3',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': '1cz65li561a',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '3p85gkub5m6',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'gqwx7acdf2r',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'jzntdh10ctc',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'jr9hile0rrx',
    'x-async': true,
    'x-index': 1,
  },
};
export const tableListDetailsGridCardWithUsers = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '0.21.0-alpha.10',
    properties: {
      cr778m2s24b: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '0.21.0-alpha.10',
        properties: {
          mtvuctz9nln: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.10',
            properties: {
              n2h7bgcnmft: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.10',
                properties: {
                  '3mgaglh07zn': {
                    'x-uid': 'qap0rl4ojtt',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [
                      {
                        uid: '87oho41ocly',
                      },
                      {
                        uid: 'z8nm5a686ru',
                      },
                      {
                        uid: '8r9z94p9xnx',
                      },
                      {
                        uid: 'l3moaf25jp5',
                      },
                    ],
                    'x-filter-operators': {},
                    'x-app-version': '0.21.0-alpha.10',
                    properties: {
                      '7xyrsf4wz0x': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '0.21.0-alpha.10',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              f32w9of6t8w: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '0.21.0-alpha.10',
                                properties: {
                                  zwcvic48sjw: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '0.21.0-alpha.10',
                                    properties: {
                                      nickname: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        required: false,
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FilterFormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '0.21.0-alpha.10',
                                        'x-uid': 'ydlo0c89wdh',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '33f9pka9t4a',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'rxob0dqm4ti',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'xt1yrbf61ey',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '9ez6m3orj5v': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              m5xe3izm0gk: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Filter records") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useFilterBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': 'y5vvz2euk2y',
                                'x-async': false,
                                'x-index': 1,
                              },
                              '09epv6b3mn4': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Reset to empty") }}',
                                'x-component': 'Action',
                                'x-use-component-props': 'useResetBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': '5vpkp83rph3',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'zf43n9dbs54',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': '9q9zncjd4w2',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '4n241vjjnat',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'jj4g1pqplz0',
            'x-async': false,
            'x-index': 1,
          },
          lyfitkgfomn: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.10',
            properties: {
              d2loy3w2fxb: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.10',
                properties: {
                  g0chhqgtije: {
                    'x-uid': '87oho41ocly',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                      dataLoadingMode: 'auto',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '0.21.0-alpha.10',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.10',
                        'x-uid': '6aql84zyi03',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '3l0fm5rubqj': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.10',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-designer': 'TableV2.ActionColumnDesigner',
                            'x-initializer': 'table:configureItemActions',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              s0isj3k1i4s: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': '37fsp76bdkc',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'zcr3aozamjs',
                            'x-async': false,
                            'x-index': 1,
                          },
                          n71mwnn8fih: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': '51u6u90cnjj',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '7b1mnyjunnu',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'voc6heqeia5',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'p9vc09wamxd',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '0sbypqmnryr',
            'x-async': false,
            'x-index': 2,
          },
          duqdkbvoiml: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.10',
            properties: {
              wjegs9mtrg0: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.10',
                properties: {
                  pw4v8eyjbva: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '0.21.0-alpha.10',
                    properties: {
                      gznqb4qnq1j: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '0.21.0-alpha.10',
                        properties: {
                          g4n3gz979si: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '0.21.0-alpha.10',
                            'x-uid': '88cgn25ei3u',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              '9j8mt2y6dir': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '0.21.0-alpha.10',
                                properties: {
                                  xllozot5o8v: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '0.21.0-alpha.10',
                                    properties: {
                                      nickname: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '0.21.0-alpha.10',
                                        'x-uid': 'ime4o8jj7cb',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'qoeeuz07ipi',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'ybxdrkaabp6',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '5qxfrmw9v10',
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '0.21.0-alpha.10',
                            'x-uid': 'ff013qc60ku',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': '9mnrya5naya',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'z8nm5a686ru',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'zqg7kc539eg',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'tr6bc7q5rbj',
            'x-async': false,
            'x-index': 3,
          },
          nionk5pna5j: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.10',
            properties: {
              krq9mqr002o: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.10',
                properties: {
                  z4ap99q153z: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'List.Decorator',
                    'x-use-decorator-props': 'useListBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 10,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'CardItem',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:list',
                    'x-app-version': '0.21.0-alpha.10',
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'list:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.10',
                        'x-uid': 'e9ofoxfnp4p',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'List',
                        'x-use-component-props': 'useListBlockProps',
                        'x-app-version': '0.21.0-alpha.10',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'List.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useListItemProps',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '0.21.0-alpha.10',
                                properties: {
                                  '8i3qhotplq6': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '0.21.0-alpha.10',
                                    properties: {
                                      '2s6mh1m6k30': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '0.21.0-alpha.10',
                                        properties: {
                                          nickname: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'users.nickname',
                                            'x-component-props': {},
                                            'x-app-version': '0.21.0-alpha.10',
                                            'x-uid': '896a5lq186e',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': '50mxu5xy97q',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '7m5lizlq1wy',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '3iu0ucyv5bs',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'list:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useListActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': 'npq4oaxn416',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '5bahuz50iy1',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': '9ndib2b7fm1',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': '8r9z94p9xnx',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '8rmmtjy26m0',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '9ys6s753kqx',
            'x-async': false,
            'x-index': 4,
          },
          uxa0ko09icm: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.10',
            properties: {
              un6k8w1yhho: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.10',
                properties: {
                  apiglros26s: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'GridCard.Decorator',
                    'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 12,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'BlockItem',
                    'x-use-component-props': 'useGridCardBlockItemProps',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:gridCard',
                    'x-app-version': '0.21.0-alpha.10',
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'gridCard:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.10',
                        'x-uid': 'ldyyrqzeovj',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'GridCard',
                        'x-use-component-props': 'useGridCardBlockProps',
                        'x-app-version': '0.21.0-alpha.10',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'GridCard.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useGridCardItemProps',
                            'x-app-version': '0.21.0-alpha.10',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '0.21.0-alpha.10',
                                properties: {
                                  zy65t5ha9sn: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '0.21.0-alpha.10',
                                    properties: {
                                      f09pppvdpog: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '0.21.0-alpha.10',
                                        properties: {
                                          nickname: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'users.nickname',
                                            'x-component-props': {},
                                            'x-app-version': '0.21.0-alpha.10',
                                            'x-uid': '17k3vzknvos',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'sepmt6gjh9h',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'oz5bq534f48',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'v9o67r9f8yb',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'gridCard:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useGridCardActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '0.21.0-alpha.10',
                                'x-uid': 'u67n3w1w28m',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '52ylnt42mpw',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'v5rwewic6fc',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'l3moaf25jp5',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'oqbdqo6t7ja',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'keyeveh0fmc',
            'x-async': false,
            'x-index': 5,
          },
        },
        'x-uid': 'ep6n4ig8dlt',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'tfsrtgf2qy9',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
};

export const detailBlockWithLinkageRule = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.0.0-alpha.10',
    properties: {
      yww8wuk4e6y: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.0.0-alpha.10',
        properties: {
          d07v0epwwvk: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.10',
            properties: {
              rp4u5qpr8ez: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.10',
                properties: {
                  '9mqhyap0ls1': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '1.0.0-alpha.10',
                    properties: {
                      '2lz9u3g81rl': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '1.0.0-alpha.10',
                        properties: {
                          s9ea5o8cj8w: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '1.0.0-alpha.10',
                            'x-uid': 'ysmy9ooyviq',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            'x-uid': '4qe5inwtjq9',
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '1.0.0-alpha.10',
                            'x-linkage-rules': [
                              {
                                condition: {
                                  $and: [
                                    {
                                      username: {
                                        $includes: 'nocobase',
                                      },
                                    },
                                  ],
                                },
                                actions: [
                                  {
                                    targetFields: ['email'],
                                    operator: 'none',
                                  },
                                ],
                              },
                            ],
                            properties: {
                              fuq132swnbk: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.10',
                                properties: {
                                  bpajop3lve7: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.10',
                                    properties: {
                                      nickname: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.0-alpha.10',
                                        'x-uid': 'zww3rhl5gd9',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'qm681tg8gpa',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'lae2aq0m2x4',
                                'x-async': false,
                                'x-index': 1,
                              },
                              ztf9sdine5o: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.10',
                                properties: {
                                  aodkupx1hh2: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.10',
                                    properties: {
                                      username: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.username',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.0-alpha.10',
                                        'x-uid': 'n72gcozfvos',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'fn8r9dxds5b',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'gocs8rfidyr',
                                'x-async': false,
                                'x-index': 2,
                              },
                              cfrvhjohq80: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.10',
                                properties: {
                                  l2ywhliyphy: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.10',
                                    properties: {
                                      email: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.email',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.0-alpha.10',
                                        'x-uid': 'sp2v1hu1q82',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'nkvczy7p4wt',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '740moxzkbnw',
                                'x-async': false,
                                'x-index': 3,
                              },
                            },
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '1.0.0-alpha.10',
                            'x-uid': 'mz4fbexxkj1',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'ymkygy5gq36',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'le8ml68mhbc',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'o5bsy38grwy',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'i5dnts4va3e',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'vxpirluqi4j',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'bygf7rii5pb',
    'x-async': true,
    'x-index': 1,
  },
};

//翻页
export const detailsBlockWithLinkageRule = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.0.0-alpha.15',
    properties: {
      s0cyq6zg42k: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.0.0-alpha.15',
        properties: {
          vtjrbevb2r2: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.0-alpha.15',
            properties: {
              ql4nxn8v031: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.0-alpha.15',
                properties: {
                  '17o39djmjom': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'roles:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'roles',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '1.0.0-alpha.15',
                    properties: {
                      vge14uwsfcx: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '1.0.0-alpha.15',
                        properties: {
                          p1oiur6ch01: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '1.0.0-alpha.15',
                            'x-uid': 'o6l10d94uyu',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            'x-uid': 'ndhiwwmhgd3',
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '1.0.0-alpha.15',
                            'x-linkage-rules': [
                              {
                                condition: {
                                  $and: [
                                    {
                                      name: {
                                        $includes: 'admin',
                                      },
                                    },
                                  ],
                                },
                                actions: [
                                  {
                                    targetFields: ['title'],
                                    operator: 'none',
                                  },
                                ],
                              },
                            ],
                            properties: {
                              '12azgfqtzgg': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.15',
                                properties: {
                                  '3huf8qty79r': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.15',
                                    properties: {
                                      name: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'roles.name',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.0-alpha.15',
                                        'x-uid': 'nlga5pyt4nh',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'nt7vszbunlp',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '5ipi32fnhbw',
                                'x-async': false,
                                'x-index': 1,
                              },
                              '8jptrhtlnau': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.0-alpha.15',
                                properties: {
                                  kjmsoxflttl: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.0-alpha.15',
                                    properties: {
                                      title: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'roles.title',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.0-alpha.15',
                                        'x-uid': '018038od8qu',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '1tf62y66ucg',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '53tuarteip2',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '1.0.0-alpha.15',
                            'x-uid': 'fi9s6c663yz',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'nbrmybpedgx',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': '6j7ofcubttd',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'zv7yvwqnaap',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'boyw74bb12g',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': '1mcq8no0n7j',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'duuivmvaoaa',
    'x-async': true,
    'x-index': 1,
  },
};
export const TableBlockWithDataScope = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      an34615vknp: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          '62fssedpl0w': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.1-alpha.2',
            properties: {
              '93tyrk65qym': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.1-alpha.2',
                properties: {
                  '0b7maevxkfs': {
                    'x-uid': 'uqnziogzhkq',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [
                      {
                        uid: 'yg26txxpq4l',
                      },
                    ],
                    'x-app-version': '1.0.1-alpha.2',
                    properties: {
                      swmsovm7d4t: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '1.0.1-alpha.2',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '1.0.1-alpha.2',
                            properties: {
                              mzd4ludncd8: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.1-alpha.2',
                                properties: {
                                  w67kg0y8wn6: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.1-alpha.2',
                                    properties: {
                                      nickname: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        required: false,
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FilterFormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-use-decorator-props': 'useFormItemProps',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.1-alpha.2',
                                        'x-uid': '3azj6wv3v2r',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '1jmg205s2sa',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'wjoskqiuk5b',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 't3gxqyl52ca',
                            'x-async': false,
                            'x-index': 1,
                          },
                          ceahs5hahgg: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '1.0.1-alpha.2',
                            properties: {
                              xgjhwz2ln8l: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Filter") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useFilterBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.2',
                                'x-uid': 'ev40o2gk87b',
                                'x-async': false,
                                'x-index': 1,
                              },
                              qxhdilp319s: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Reset") }}',
                                'x-component': 'Action',
                                'x-use-component-props': 'useResetBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.2',
                                'x-uid': '559bivcwabh',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '8q25gzurfeh',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'v3mwerc709f',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '952up9gdrhf',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'da58ptz1cie',
            'x-async': false,
            'x-index': 1,
          },
          dxd8oaoh4a7: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.1-alpha.2',
            properties: {
              '27ijajdwyd2': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.1-alpha.2',
                properties: {
                  '7yz5da41nt3': {
                    'x-uid': 'yg26txxpq4l',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                        filter: {
                          $and: [
                            {
                              nickname: {
                                $includes: '{{$user.nickname}}',
                              },
                            },
                          ],
                        },
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                      dataLoadingMode: 'manual',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.0.1-alpha.2',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.2',
                        'x-uid': '73fy8oxxxk9',
                        'x-async': false,
                        'x-index': 1,
                      },
                      wo6mmz3gm4e: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.2',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.0.1-alpha.2',
                            properties: {
                              k96lkyh071r: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.0.1-alpha.2',
                                'x-uid': 'e0u8ctx63xa',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'kscb8lws4pt',
                            'x-async': false,
                            'x-index': 1,
                          },
                          tpeettygzf9: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.1-alpha.2',
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.1-alpha.2',
                                'x-uid': 'fyeg48brfk6',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '1zzxomha3yb',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'f8zia156lux',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'j9c5x3pl112',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'qkazgqmj5zk',
            'x-async': false,
            'x-index': 2,
          },
        },
        'x-uid': 'iabe2fdu20i',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'dxf2yvxqvra',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
};
