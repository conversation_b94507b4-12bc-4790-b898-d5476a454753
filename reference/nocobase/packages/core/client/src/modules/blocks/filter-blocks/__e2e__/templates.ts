/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const tableDetailsListGridCardWithUsers = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '0.21.0-alpha.15',
    properties: {
      '9uvv7ydmvmd': {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '0.21.0-alpha.15',
        properties: {
          voe1jn124m6: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            properties: {
              ykfrrluma0h: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                properties: {
                  '5ovawvsk2m1': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '0.21.0-alpha.15',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        'x-uid': 'gthqv4uaubi',
                        'x-async': false,
                        'x-index': 1,
                      },
                      '2cptag71atw': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-designer': 'TableV2.ActionColumnDesigner',
                            'x-initializer': 'table:configureItemActions',
                            'x-app-version': '0.21.0-alpha.15',
                            properties: {
                              xbfvdqe0lcs: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '0.21.0-alpha.15',
                                'x-uid': 'tzkjed29ec4',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'a3us1cfrwia',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '7mr9bnpcrwm': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '0.21.0-alpha.15',
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '0.21.0-alpha.15',
                                'x-uid': 'rydna464x7i',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'gejlmm27aa7',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'x6ktlhol700',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': '79xmcus0pn8',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '1vwn4akg64m',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'ld956ku384v',
            'x-async': false,
            'x-index': 1,
          },
          ij9nh362xlx: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            properties: {
              q853rp6cv2o: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                properties: {
                  '0rancu18c3t': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'DetailsBlockProvider',
                    'x-use-decorator-props': 'useDetailsWithPaginationDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 1,
                      },
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:detailsWithPagination',
                    'x-component': 'CardItem',
                    'x-app-version': '0.21.0-alpha.15',
                    properties: {
                      k0gp4d6bp47: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'Details',
                        'x-read-pretty': true,
                        'x-use-component-props': 'useDetailsWithPaginationProps',
                        'x-app-version': '0.21.0-alpha.15',
                        properties: {
                          '46w4xjjxqsr': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'details:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              style: {
                                marginBottom: 24,
                              },
                            },
                            'x-app-version': '0.21.0-alpha.15',
                            'x-uid': 'beelkbsivi8',
                            'x-async': false,
                            'x-index': 1,
                          },
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'details:configureFields',
                            'x-app-version': '0.21.0-alpha.15',
                            properties: {
                              tht6lqgyku1: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '0.21.0-alpha.15',
                                properties: {
                                  '0k4bzq9ci5i': {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '0.21.0-alpha.15',
                                    properties: {
                                      nickname: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '0.21.0-alpha.15',
                                        'x-uid': 'pl3owbdqfo3',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'bjv8hi7diq2',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'x3wmij5qx8a',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '1x30b6yfx99',
                            'x-async': false,
                            'x-index': 2,
                          },
                          pagination: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Pagination',
                            'x-use-component-props': 'useDetailsPaginationProps',
                            'x-app-version': '0.21.0-alpha.15',
                            'x-uid': '23jk7ja9jkk',
                            'x-async': false,
                            'x-index': 3,
                          },
                        },
                        'x-uid': 'juczg2jh500',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': '4whfpb90dn7',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'vvbsg0s5tcr',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'ttr211kqcox',
            'x-async': false,
            'x-index': 2,
          },
          vgr33x8jh3a: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            properties: {
              w7yqoi8316a: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                properties: {
                  '210p1dq7a1s': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'List.Decorator',
                    'x-use-decorator-props': 'useListBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 10,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'CardItem',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:list',
                    'x-app-version': '0.21.0-alpha.15',
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'list:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        'x-uid': 'b43enq6x37m',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'List',
                        'x-use-component-props': 'useListBlockProps',
                        'x-app-version': '0.21.0-alpha.15',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'List.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useListItemProps',
                            'x-app-version': '0.21.0-alpha.15',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '0.21.0-alpha.15',
                                properties: {
                                  t4iiyx5kwxc: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '0.21.0-alpha.15',
                                    properties: {
                                      xg6b36sfo0j: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '0.21.0-alpha.15',
                                        properties: {
                                          nickname: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'users.nickname',
                                            'x-component-props': {},
                                            'x-app-version': '0.21.0-alpha.15',
                                            'x-uid': 'xxf3ntw195s',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'd22jvs6ynfn',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'zke7r8kitk6',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'o0dlql5q0dc',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'list:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useListActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '0.21.0-alpha.15',
                                'x-uid': 'pl9nyokfl7u',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '1hr9dfxpxo2',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': 'sh38xfoaerm',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'qztm5xvlwxu',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'fvmc5rku8b8',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '1weaufeee1b',
            'x-async': false,
            'x-index': 3,
          },
          '11o6g2p2ynq': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            properties: {
              '852c7m9e5we': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                properties: {
                  zw3qynnh2w9: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-acl-action': 'users:view',
                    'x-decorator': 'GridCard.Decorator',
                    'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      readPretty: true,
                      action: 'list',
                      params: {
                        pageSize: 12,
                      },
                      runWhenParamsChanged: true,
                      rowKey: 'id',
                    },
                    'x-component': 'BlockItem',
                    'x-use-component-props': 'useGridCardBlockItemProps',
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:gridCard',
                    'x-app-version': '0.21.0-alpha.15',
                    properties: {
                      actionBar: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'gridCard:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        'x-uid': 'y4r3n4ux6yi',
                        'x-async': false,
                        'x-index': 1,
                      },
                      list: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-component': 'GridCard',
                        'x-use-component-props': 'useGridCardBlockProps',
                        'x-app-version': '0.21.0-alpha.15',
                        properties: {
                          item: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'object',
                            'x-component': 'GridCard.Item',
                            'x-read-pretty': true,
                            'x-use-component-props': 'useGridCardItemProps',
                            'x-app-version': '0.21.0-alpha.15',
                            properties: {
                              grid: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid',
                                'x-initializer': 'details:configureFields',
                                'x-app-version': '0.21.0-alpha.15',
                                properties: {
                                  t8dr9bz30ic: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Row',
                                    'x-app-version': '0.21.0-alpha.15',
                                    properties: {
                                      l2y9n9omym2: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        'x-component': 'Grid.Col',
                                        'x-app-version': '0.21.0-alpha.15',
                                        properties: {
                                          nickname: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'string',
                                            'x-toolbar': 'FormItemSchemaToolbar',
                                            'x-settings': 'fieldSettings:FormItem',
                                            'x-component': 'CollectionField',
                                            'x-decorator': 'FormItem',
                                            'x-collection-field': 'users.nickname',
                                            'x-component-props': {},
                                            'x-app-version': '0.21.0-alpha.15',
                                            'x-uid': 'mmve9lw6hq5',
                                            'x-async': false,
                                            'x-index': 1,
                                          },
                                        },
                                        'x-uid': 'nddibjuay67',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '42wqrm3gumo',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'uxbrmj5gov0',
                                'x-async': false,
                                'x-index': 1,
                              },
                              actionBar: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-align': 'left',
                                'x-initializer': 'gridCard:configureItemActions',
                                'x-component': 'ActionBar',
                                'x-use-component-props': 'useGridCardActionBarProps',
                                'x-component-props': {
                                  layout: 'one-column',
                                },
                                'x-app-version': '0.21.0-alpha.15',
                                'x-uid': 'nz3gam7wfcp',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '549z367nhtq',
                            'x-async': false,
                            'x-index': 1,
                          },
                        },
                        'x-uid': '8jqbs62h8al',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': '2nigd3g90cu',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'r04q13e4bae',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'kjouclctu40',
            'x-async': false,
            'x-index': 4,
          },
        },
        'x-uid': 'dcjkmu3qkal',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'ifbigjtfclg',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
};
export const detailsTableListGridCardInPopup = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '0.21.0-alpha.15',
    'x-index': 1,
    properties: {
      mvuyz1mgstk: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '0.21.0-alpha.15',
        'x-index': 1,
        properties: {
          woexs8tqxb1: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            'x-index': 1,
            properties: {
              tmxv3l829di: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                'x-index': 1,
                properties: {
                  '6x2w9162eng': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '0.21.0-alpha.15',
                    'x-index': 1,
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        'x-index': 1,
                        'x-uid': 'lg8ox84e2yj',
                        'x-async': false,
                      },
                      rl3jmqxyb8r: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '0.21.0-alpha.15',
                        'x-index': 2,
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-designer': 'TableV2.ActionColumnDesigner',
                            'x-initializer': 'table:configureItemActions',
                            'x-app-version': '0.21.0-alpha.15',
                            'x-index': 1,
                            properties: {
                              n69pgzca87s: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '0.21.0-alpha.15',
                                'x-index': 1,
                                properties: {
                                  hihiwavggft: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    title: 'View record',
                                    'x-action': 'view',
                                    'x-toolbar': 'ActionSchemaToolbar',
                                    'x-settings': 'actionSettings:view',
                                    'x-component': 'Action.Link',
                                    'x-component-props': {
                                      openMode: 'drawer',
                                      danger: false,
                                    },
                                    'x-decorator': 'ACLActionProvider',
                                    'x-designer-props': {
                                      linkageAction: true,
                                    },
                                    'x-index': 1,
                                    properties: {
                                      drawer: {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'void',
                                        title: '{{ t("View record") }}',
                                        'x-component': 'Action.Container',
                                        'x-component-props': {
                                          className: 'nb-action-popup',
                                        },
                                        'x-index': 1,
                                        properties: {
                                          tabs: {
                                            _isJSONSchemaObject: true,
                                            version: '2.0',
                                            type: 'void',
                                            'x-component': 'Tabs',
                                            'x-component-props': {},
                                            'x-initializer': 'popup:addTab',
                                            'x-index': 1,
                                            properties: {
                                              tab1: {
                                                _isJSONSchemaObject: true,
                                                version: '2.0',
                                                type: 'void',
                                                title: '{{t("Details")}}',
                                                'x-component': 'Tabs.TabPane',
                                                'x-designer': 'Tabs.Designer',
                                                'x-component-props': {},
                                                'x-index': 1,
                                                properties: {
                                                  grid: {
                                                    _isJSONSchemaObject: true,
                                                    version: '2.0',
                                                    type: 'void',
                                                    'x-component': 'Grid',
                                                    'x-initializer': 'popup:common:addBlock',
                                                    'x-index': 1,
                                                    properties: {
                                                      '7c9cnouotll': {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 1,
                                                        properties: {
                                                          zhyzowuuom8: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              xwlsydiovoz: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users.roles:view',
                                                                'x-decorator': 'DetailsBlockProvider',
                                                                'x-use-decorator-props':
                                                                  'useDetailsWithPaginationDecoratorProps',
                                                                'x-decorator-props': {
                                                                  dataSource: 'main',
                                                                  association: 'users.roles',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 1,
                                                                  },
                                                                },
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:detailsWithPagination',
                                                                'x-component': 'CardItem',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  '5nz3tnm18s5': {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-component': 'Details',
                                                                    'x-read-pretty': true,
                                                                    'x-use-component-props':
                                                                      'useDetailsWithPaginationProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    properties: {
                                                                      '7l2wnp2u2p0': {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-initializer': 'details:configureActions',
                                                                        'x-component': 'ActionBar',
                                                                        'x-component-props': {
                                                                          style: {
                                                                            marginBottom: 24,
                                                                          },
                                                                        },
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        'x-uid': 'a1wrnq3wl89',
                                                                        'x-async': false,
                                                                      },
                                                                      grid: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-component': 'Grid',
                                                                        'x-initializer': 'details:configureFields',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 2,
                                                                        properties: {
                                                                          '74it50iqg6m': {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid.Row',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            properties: {
                                                                              z4ft3bbl2k3: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Col',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                properties: {
                                                                                  name: {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'string',
                                                                                    'x-toolbar':
                                                                                      'FormItemSchemaToolbar',
                                                                                    'x-settings':
                                                                                      'fieldSettings:FormItem',
                                                                                    'x-component': 'CollectionField',
                                                                                    'x-decorator': 'FormItem',
                                                                                    'x-collection-field': 'roles.name',
                                                                                    'x-component-props': {},
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    'x-uid': 'qijbpeoib4a',
                                                                                    'x-async': false,
                                                                                    'x-index': 1,
                                                                                  },
                                                                                },
                                                                                'x-uid': 'jz6jqgievn7',
                                                                                'x-async': false,
                                                                                'x-index': 1,
                                                                              },
                                                                            },
                                                                            'x-uid': 'vwlkijbckjy',
                                                                            'x-async': false,
                                                                            'x-index': 2,
                                                                          },
                                                                        },
                                                                        'x-uid': 'mbdi5dp0uy9',
                                                                        'x-async': false,
                                                                      },
                                                                      pagination: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-component': 'Pagination',
                                                                        'x-use-component-props':
                                                                          'useDetailsPaginationProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 3,
                                                                        'x-uid': 'esorm0agfd7',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': 'zllcmkba4ag',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'as4sc43hmo6',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': 'x2hydkcig3j',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'wr0h16g0agh',
                                                        'x-async': false,
                                                      },
                                                      k9hwxfpvs5z: {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 2,
                                                        properties: {
                                                          '8071u6sqhjx': {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              '2ddo3f0gkzu': {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users:view',
                                                                'x-decorator': 'DetailsBlockProvider',
                                                                'x-use-decorator-props':
                                                                  'useDetailsWithPaginationDecoratorProps',
                                                                'x-decorator-props': {
                                                                  dataSource: 'main',
                                                                  collection: 'users',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 1,
                                                                  },
                                                                },
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:detailsWithPagination',
                                                                'x-component': 'CardItem',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  qi41mfgv9k6: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-component': 'Details',
                                                                    'x-read-pretty': true,
                                                                    'x-use-component-props':
                                                                      'useDetailsWithPaginationProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    properties: {
                                                                      '1l8wiojvy33': {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-initializer': 'details:configureActions',
                                                                        'x-component': 'ActionBar',
                                                                        'x-component-props': {
                                                                          style: {
                                                                            marginBottom: 24,
                                                                          },
                                                                        },
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        'x-uid': '9m35j2awac8',
                                                                        'x-async': false,
                                                                      },
                                                                      grid: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-component': 'Grid',
                                                                        'x-initializer': 'details:configureFields',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 2,
                                                                        properties: {
                                                                          spw1xven1tm: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid.Row',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            properties: {
                                                                              '10o4mgxz8ya': {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Col',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                'x-index': 1,
                                                                                properties: {
                                                                                  nickname: {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'string',
                                                                                    'x-toolbar':
                                                                                      'FormItemSchemaToolbar',
                                                                                    'x-settings':
                                                                                      'fieldSettings:FormItem',
                                                                                    'x-component': 'CollectionField',
                                                                                    'x-decorator': 'FormItem',
                                                                                    'x-collection-field':
                                                                                      'users.nickname',
                                                                                    'x-component-props': {},
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    'x-index': 1,
                                                                                    'x-uid': 'heu60o0fjvw',
                                                                                    'x-async': false,
                                                                                  },
                                                                                },
                                                                                'x-uid': 'eu466gnly83',
                                                                                'x-async': false,
                                                                              },
                                                                            },
                                                                            'x-uid': 'rkrmqf4qtvt',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': 'zqd7wt6x7sf',
                                                                        'x-async': false,
                                                                      },
                                                                      pagination: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-component': 'Pagination',
                                                                        'x-use-component-props':
                                                                          'useDetailsPaginationProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 3,
                                                                        'x-uid': '6ks5zlg89ug',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': '340j1itrpoq',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'iwq7us57r9v',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': 'akdgr36tk4p',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': '082x4pgh4o5',
                                                        'x-async': false,
                                                      },
                                                      '9a5au8wklr7': {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 3,
                                                        properties: {
                                                          l19mz03ow8s: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              '9mo13nzc1ac': {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-decorator': 'TableBlockProvider',
                                                                'x-acl-action': 'users.roles:list',
                                                                'x-use-decorator-props': 'useTableBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  association: 'users.roles',
                                                                  dataSource: 'main',
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 20,
                                                                  },
                                                                  rowKey: 'name',
                                                                  showIndex: true,
                                                                  dragSort: false,
                                                                },
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:table',
                                                                'x-component': 'CardItem',
                                                                'x-filter-targets': [],
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actions: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'table:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': 'i880m2zvoci',
                                                                    'x-async': false,
                                                                  },
                                                                  xv44zf1hv4q: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-initializer': 'table:configureColumns',
                                                                    'x-component': 'TableV2',
                                                                    'x-use-component-props': 'useTableBlockProps',
                                                                    'x-component-props': {
                                                                      rowKey: 'id',
                                                                      rowSelection: {
                                                                        type: 'checkbox',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      actions: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        title: '{{ t("Actions") }}',
                                                                        'x-action-column': 'actions',
                                                                        'x-decorator': 'TableV2.Column.ActionBar',
                                                                        'x-component': 'TableV2.Column',
                                                                        'x-designer': 'TableV2.ActionColumnDesigner',
                                                                        'x-initializer': 'table:configureItemActions',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          r3t97c5zx23: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-decorator': 'DndContext',
                                                                            'x-component': 'Space',
                                                                            'x-component-props': {
                                                                              split: '|',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            'x-uid': 'xpt2zxugy1d',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': '31d900yerb0',
                                                                        'x-async': false,
                                                                      },
                                                                      '2kv9mbp8cpu': {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-decorator': 'TableV2.Column.Decorator',
                                                                        'x-toolbar': 'TableColumnSchemaToolbar',
                                                                        'x-settings': 'fieldSettings:TableColumn',
                                                                        'x-component': 'TableV2.Column',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        properties: {
                                                                          name: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            'x-collection-field': 'roles.name',
                                                                            'x-component': 'CollectionField',
                                                                            'x-component-props': {
                                                                              ellipsis: true,
                                                                            },
                                                                            'x-read-pretty': true,
                                                                            'x-decorator': null,
                                                                            'x-decorator-props': {
                                                                              labelStyle: {
                                                                                display: 'none',
                                                                              },
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-uid': '9r6tnnrmpp6',
                                                                            'x-async': false,
                                                                            'x-index': 1,
                                                                          },
                                                                        },
                                                                        'x-uid': '5ooizbci5fl',
                                                                        'x-async': false,
                                                                        'x-index': 3,
                                                                      },
                                                                    },
                                                                    'x-uid': 'andayw3gea9',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'vp3qggdfnyi',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': 'e14xrj73u6q',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': '2yddfu4h6lf',
                                                        'x-async': false,
                                                      },
                                                      '8wk52v941az': {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 4,
                                                        properties: {
                                                          '023qd6oiek8': {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              eo2vyqropkd: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-decorator': 'TableBlockProvider',
                                                                'x-acl-action': 'users:list',
                                                                'x-use-decorator-props': 'useTableBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  collection: 'users',
                                                                  dataSource: 'main',
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 20,
                                                                  },
                                                                  rowKey: 'id',
                                                                  showIndex: true,
                                                                  dragSort: false,
                                                                },
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:table',
                                                                'x-component': 'CardItem',
                                                                'x-filter-targets': [],
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actions: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'table:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': 'jwlkscns8n1',
                                                                    'x-async': false,
                                                                  },
                                                                  kl7mhxaxc9v: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-initializer': 'table:configureColumns',
                                                                    'x-component': 'TableV2',
                                                                    'x-use-component-props': 'useTableBlockProps',
                                                                    'x-component-props': {
                                                                      rowKey: 'id',
                                                                      rowSelection: {
                                                                        type: 'checkbox',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      actions: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        title: '{{ t("Actions") }}',
                                                                        'x-action-column': 'actions',
                                                                        'x-decorator': 'TableV2.Column.ActionBar',
                                                                        'x-component': 'TableV2.Column',
                                                                        'x-designer': 'TableV2.ActionColumnDesigner',
                                                                        'x-initializer': 'table:configureItemActions',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          '18yq55t8ykl': {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-decorator': 'DndContext',
                                                                            'x-component': 'Space',
                                                                            'x-component-props': {
                                                                              split: '|',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            'x-uid': 'r3grinc8qmo',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': 'jrcvgt0k2p6',
                                                                        'x-async': false,
                                                                      },
                                                                      n6txkckkail: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'void',
                                                                        'x-decorator': 'TableV2.Column.Decorator',
                                                                        'x-toolbar': 'TableColumnSchemaToolbar',
                                                                        'x-settings': 'fieldSettings:TableColumn',
                                                                        'x-component': 'TableV2.Column',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 2,
                                                                        properties: {
                                                                          nickname: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            'x-collection-field': 'users.nickname',
                                                                            'x-component': 'CollectionField',
                                                                            'x-component-props': {
                                                                              ellipsis: true,
                                                                            },
                                                                            'x-read-pretty': true,
                                                                            'x-decorator': null,
                                                                            'x-decorator-props': {
                                                                              labelStyle: {
                                                                                display: 'none',
                                                                              },
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            'x-uid': 'mk5u3buadol',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': 'meiilye1qxl',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': 'inawtjdt0h0',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': '1xi2yzl1ucm',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': '820i9h1lo87',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'yb0ff65qc9j',
                                                        'x-async': false,
                                                      },
                                                      hr7odqbec30: {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 5,
                                                        properties: {
                                                          fr3nnhqw07g: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              mjq22s4f33o: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users.roles:view',
                                                                'x-decorator': 'List.Decorator',
                                                                'x-use-decorator-props': 'useListBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  dataSource: 'main',
                                                                  association: 'users.roles',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 10,
                                                                  },
                                                                  runWhenParamsChanged: true,
                                                                  rowKey: 'name',
                                                                },
                                                                'x-component': 'CardItem',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:list',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actionBar: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'list:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': '3qyl0zrs1hf',
                                                                    'x-async': false,
                                                                  },
                                                                  list: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-component': 'List',
                                                                    'x-use-component-props': 'useListBlockProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      item: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'object',
                                                                        'x-component': 'List.Item',
                                                                        'x-read-pretty': true,
                                                                        'x-use-component-props': 'useListItemProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          grid: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid',
                                                                            'x-initializer': 'details:configureFields',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            properties: {
                                                                              rjjmaae8cfx: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Row',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                properties: {
                                                                                  '8t1ntiodcnk': {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'void',
                                                                                    'x-component': 'Grid.Col',
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    properties: {
                                                                                      name: {
                                                                                        _isJSONSchemaObject: true,
                                                                                        version: '2.0',
                                                                                        type: 'string',
                                                                                        'x-toolbar':
                                                                                          'FormItemSchemaToolbar',
                                                                                        'x-settings':
                                                                                          'fieldSettings:FormItem',
                                                                                        'x-component':
                                                                                          'CollectionField',
                                                                                        'x-decorator': 'FormItem',
                                                                                        'x-collection-field':
                                                                                          'roles.name',
                                                                                        'x-component-props': {},
                                                                                        'x-app-version':
                                                                                          '0.21.0-alpha.15',
                                                                                        'x-uid': '927dha2yk9s',
                                                                                        'x-async': false,
                                                                                        'x-index': 1,
                                                                                      },
                                                                                    },
                                                                                    'x-uid': 'rwd5op8ebfj',
                                                                                    'x-async': false,
                                                                                    'x-index': 1,
                                                                                  },
                                                                                },
                                                                                'x-uid': '42otwbvbbmh',
                                                                                'x-async': false,
                                                                                'x-index': 2,
                                                                              },
                                                                            },
                                                                            'x-uid': '4ud8e4vc6mf',
                                                                            'x-async': false,
                                                                          },
                                                                          actionBar: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-align': 'left',
                                                                            'x-initializer':
                                                                              'list:configureItemActions',
                                                                            'x-component': 'ActionBar',
                                                                            'x-use-component-props':
                                                                              'useListActionBarProps',
                                                                            'x-component-props': {
                                                                              layout: 'one-column',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 2,
                                                                            'x-uid': '2qjdc5cr2jt',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': '43q5c032qiq',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': '3yccsc0r847',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'ug1879fox45',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': '2c2do8t3mt9',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'ry956fcb84v',
                                                        'x-async': false,
                                                      },
                                                      ninas5j5lzk: {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 6,
                                                        properties: {
                                                          '1oyvrccw9oi': {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              wpqarouweuz: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users:view',
                                                                'x-decorator': 'List.Decorator',
                                                                'x-use-decorator-props': 'useListBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  collection: 'users',
                                                                  dataSource: 'main',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 10,
                                                                  },
                                                                  runWhenParamsChanged: true,
                                                                  rowKey: 'id',
                                                                },
                                                                'x-component': 'CardItem',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:list',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actionBar: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'list:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': 'c6z1pzg0v6a',
                                                                    'x-async': false,
                                                                  },
                                                                  list: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-component': 'List',
                                                                    'x-use-component-props': 'useListBlockProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      item: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'object',
                                                                        'x-component': 'List.Item',
                                                                        'x-read-pretty': true,
                                                                        'x-use-component-props': 'useListItemProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          grid: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid',
                                                                            'x-initializer': 'details:configureFields',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            properties: {
                                                                              tdecsbp01um: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Row',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                'x-index': 1,
                                                                                properties: {
                                                                                  ac7y78x5i7a: {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'void',
                                                                                    'x-component': 'Grid.Col',
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    'x-index': 1,
                                                                                    properties: {
                                                                                      nickname: {
                                                                                        _isJSONSchemaObject: true,
                                                                                        version: '2.0',
                                                                                        type: 'string',
                                                                                        'x-toolbar':
                                                                                          'FormItemSchemaToolbar',
                                                                                        'x-settings':
                                                                                          'fieldSettings:FormItem',
                                                                                        'x-component':
                                                                                          'CollectionField',
                                                                                        'x-decorator': 'FormItem',
                                                                                        'x-collection-field':
                                                                                          'users.nickname',
                                                                                        'x-component-props': {},
                                                                                        'x-app-version':
                                                                                          '0.21.0-alpha.15',
                                                                                        'x-index': 1,
                                                                                        'x-uid': 'pqbn37xtayx',
                                                                                        'x-async': false,
                                                                                      },
                                                                                    },
                                                                                    'x-uid': 'ayauhbosgfe',
                                                                                    'x-async': false,
                                                                                  },
                                                                                },
                                                                                'x-uid': '5783fsm1rpw',
                                                                                'x-async': false,
                                                                              },
                                                                            },
                                                                            'x-uid': 'b0ntistsq0k',
                                                                            'x-async': false,
                                                                          },
                                                                          actionBar: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-align': 'left',
                                                                            'x-initializer':
                                                                              'list:configureItemActions',
                                                                            'x-component': 'ActionBar',
                                                                            'x-use-component-props':
                                                                              'useListActionBarProps',
                                                                            'x-component-props': {
                                                                              layout: 'one-column',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 2,
                                                                            'x-uid': 'ul1egwbtuie',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': '9bqpvytl8b6',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': 'n7u7ief7ae1',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': '11h7ijtwywv',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': 'tzq2ojbsdbr',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'qvfqywpcskh',
                                                        'x-async': false,
                                                      },
                                                      '3ybhnijplde': {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 7,
                                                        properties: {
                                                          wy0pasojwjc: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              wuz3txf0ar4: {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users.roles:view',
                                                                'x-decorator': 'GridCard.Decorator',
                                                                'x-use-decorator-props':
                                                                  'useGridCardBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  association: 'users.roles',
                                                                  dataSource: 'main',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 12,
                                                                  },
                                                                  runWhenParamsChanged: true,
                                                                  rowKey: 'name',
                                                                },
                                                                'x-component': 'BlockItem',
                                                                'x-use-component-props': 'useGridCardBlockItemProps',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:gridCard',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actionBar: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'gridCard:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': 'q8kg5szzqyp',
                                                                    'x-async': false,
                                                                  },
                                                                  list: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-component': 'GridCard',
                                                                    'x-use-component-props': 'useGridCardBlockProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      item: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'object',
                                                                        'x-component': 'GridCard.Item',
                                                                        'x-read-pretty': true,
                                                                        'x-use-component-props': 'useGridCardItemProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          grid: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid',
                                                                            'x-initializer': 'details:configureFields',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            properties: {
                                                                              nt8y1viliug: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Row',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                properties: {
                                                                                  em5myw2xcf7: {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'void',
                                                                                    'x-component': 'Grid.Col',
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    properties: {
                                                                                      name: {
                                                                                        _isJSONSchemaObject: true,
                                                                                        version: '2.0',
                                                                                        type: 'string',
                                                                                        'x-toolbar':
                                                                                          'FormItemSchemaToolbar',
                                                                                        'x-settings':
                                                                                          'fieldSettings:FormItem',
                                                                                        'x-component':
                                                                                          'CollectionField',
                                                                                        'x-decorator': 'FormItem',
                                                                                        'x-collection-field':
                                                                                          'roles.name',
                                                                                        'x-component-props': {},
                                                                                        'x-app-version':
                                                                                          '0.21.0-alpha.15',
                                                                                        'x-uid': 'fzb5micv3dk',
                                                                                        'x-async': false,
                                                                                        'x-index': 1,
                                                                                      },
                                                                                    },
                                                                                    'x-uid': 'rqhvky214t1',
                                                                                    'x-async': false,
                                                                                    'x-index': 1,
                                                                                  },
                                                                                },
                                                                                'x-uid': 'yytayazos33',
                                                                                'x-async': false,
                                                                                'x-index': 2,
                                                                              },
                                                                            },
                                                                            'x-uid': '9w7tzn0fcdu',
                                                                            'x-async': false,
                                                                          },
                                                                          actionBar: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-align': 'left',
                                                                            'x-initializer':
                                                                              'gridCard:configureItemActions',
                                                                            'x-component': 'ActionBar',
                                                                            'x-use-component-props':
                                                                              'useGridCardActionBarProps',
                                                                            'x-component-props': {
                                                                              layout: 'one-column',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 2,
                                                                            'x-uid': 'o7j7byoqyn2',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': 'xevh2ziaxkm',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': '407s8o0t4fc',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'q6hqfnkkr6p',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': '9zx4l2mstkl',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'wkr57mhtvlo',
                                                        'x-async': false,
                                                      },
                                                      i0uj0t3cvly: {
                                                        _isJSONSchemaObject: true,
                                                        version: '2.0',
                                                        type: 'void',
                                                        'x-component': 'Grid.Row',
                                                        'x-app-version': '0.21.0-alpha.15',
                                                        'x-index': 8,
                                                        properties: {
                                                          m39rv4nfrw1: {
                                                            _isJSONSchemaObject: true,
                                                            version: '2.0',
                                                            type: 'void',
                                                            'x-component': 'Grid.Col',
                                                            'x-app-version': '0.21.0-alpha.15',
                                                            'x-index': 1,
                                                            properties: {
                                                              '1nr97kxg60h': {
                                                                _isJSONSchemaObject: true,
                                                                version: '2.0',
                                                                type: 'void',
                                                                'x-acl-action': 'users:view',
                                                                'x-decorator': 'GridCard.Decorator',
                                                                'x-use-decorator-props':
                                                                  'useGridCardBlockDecoratorProps',
                                                                'x-decorator-props': {
                                                                  collection: 'users',
                                                                  dataSource: 'main',
                                                                  readPretty: true,
                                                                  action: 'list',
                                                                  params: {
                                                                    pageSize: 12,
                                                                  },
                                                                  runWhenParamsChanged: true,
                                                                  rowKey: 'id',
                                                                },
                                                                'x-component': 'BlockItem',
                                                                'x-use-component-props': 'useGridCardBlockItemProps',
                                                                'x-toolbar': 'BlockSchemaToolbar',
                                                                'x-settings': 'blockSettings:gridCard',
                                                                'x-app-version': '0.21.0-alpha.15',
                                                                'x-index': 1,
                                                                properties: {
                                                                  actionBar: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'void',
                                                                    'x-initializer': 'gridCard:configureActions',
                                                                    'x-component': 'ActionBar',
                                                                    'x-component-props': {
                                                                      style: {
                                                                        marginBottom: 'var(--nb-spacing)',
                                                                      },
                                                                    },
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 1,
                                                                    'x-uid': 'efqh1behkbd',
                                                                    'x-async': false,
                                                                  },
                                                                  list: {
                                                                    _isJSONSchemaObject: true,
                                                                    version: '2.0',
                                                                    type: 'array',
                                                                    'x-component': 'GridCard',
                                                                    'x-use-component-props': 'useGridCardBlockProps',
                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                    'x-index': 2,
                                                                    properties: {
                                                                      item: {
                                                                        _isJSONSchemaObject: true,
                                                                        version: '2.0',
                                                                        type: 'object',
                                                                        'x-component': 'GridCard.Item',
                                                                        'x-read-pretty': true,
                                                                        'x-use-component-props': 'useGridCardItemProps',
                                                                        'x-app-version': '0.21.0-alpha.15',
                                                                        'x-index': 1,
                                                                        properties: {
                                                                          grid: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-component': 'Grid',
                                                                            'x-initializer': 'details:configureFields',
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 1,
                                                                            properties: {
                                                                              io80apkug3q: {
                                                                                _isJSONSchemaObject: true,
                                                                                version: '2.0',
                                                                                type: 'void',
                                                                                'x-component': 'Grid.Row',
                                                                                'x-app-version': '0.21.0-alpha.15',
                                                                                'x-index': 1,
                                                                                properties: {
                                                                                  sx8h26dxc5s: {
                                                                                    _isJSONSchemaObject: true,
                                                                                    version: '2.0',
                                                                                    type: 'void',
                                                                                    'x-component': 'Grid.Col',
                                                                                    'x-app-version': '0.21.0-alpha.15',
                                                                                    'x-index': 1,
                                                                                    properties: {
                                                                                      nickname: {
                                                                                        _isJSONSchemaObject: true,
                                                                                        version: '2.0',
                                                                                        type: 'string',
                                                                                        'x-toolbar':
                                                                                          'FormItemSchemaToolbar',
                                                                                        'x-settings':
                                                                                          'fieldSettings:FormItem',
                                                                                        'x-component':
                                                                                          'CollectionField',
                                                                                        'x-decorator': 'FormItem',
                                                                                        'x-collection-field':
                                                                                          'users.nickname',
                                                                                        'x-component-props': {},
                                                                                        'x-app-version':
                                                                                          '0.21.0-alpha.15',
                                                                                        'x-index': 1,
                                                                                        'x-uid': '6hq7aihqc9t',
                                                                                        'x-async': false,
                                                                                      },
                                                                                    },
                                                                                    'x-uid': 'sax7hjy7fyc',
                                                                                    'x-async': false,
                                                                                  },
                                                                                },
                                                                                'x-uid': '2uwt2a22r5p',
                                                                                'x-async': false,
                                                                              },
                                                                            },
                                                                            'x-uid': '65nejltnsco',
                                                                            'x-async': false,
                                                                          },
                                                                          actionBar: {
                                                                            _isJSONSchemaObject: true,
                                                                            version: '2.0',
                                                                            type: 'void',
                                                                            'x-align': 'left',
                                                                            'x-initializer':
                                                                              'gridCard:configureItemActions',
                                                                            'x-component': 'ActionBar',
                                                                            'x-use-component-props':
                                                                              'useGridCardActionBarProps',
                                                                            'x-component-props': {
                                                                              layout: 'one-column',
                                                                            },
                                                                            'x-app-version': '0.21.0-alpha.15',
                                                                            'x-index': 2,
                                                                            'x-uid': '9x26208x0pm',
                                                                            'x-async': false,
                                                                          },
                                                                        },
                                                                        'x-uid': 'eiu6r7z2p1c',
                                                                        'x-async': false,
                                                                      },
                                                                    },
                                                                    'x-uid': 'q0h0ynhhpcn',
                                                                    'x-async': false,
                                                                  },
                                                                },
                                                                'x-uid': 'o1nqvxhyuze',
                                                                'x-async': false,
                                                              },
                                                            },
                                                            'x-uid': 'vz4codfqjao',
                                                            'x-async': false,
                                                          },
                                                        },
                                                        'x-uid': 'xzpmxf4e11e',
                                                        'x-async': false,
                                                      },
                                                    },
                                                    'x-uid': '7to7okwtaa5',
                                                    'x-async': false,
                                                  },
                                                },
                                                'x-uid': 'k9msdtdomeu',
                                                'x-async': false,
                                              },
                                            },
                                            'x-uid': '0qgodqhza93',
                                            'x-async': false,
                                          },
                                        },
                                        'x-uid': '1jtu40ldbr7',
                                        'x-async': false,
                                      },
                                    },
                                    'x-uid': '6v9eio5wgf8',
                                    'x-async': false,
                                  },
                                },
                                'x-uid': 'ji8z4q5fzeb',
                                'x-async': false,
                              },
                            },
                            'x-uid': 'myttymhbq4i',
                            'x-async': false,
                          },
                        },
                        'x-uid': 'cpxxz872w05',
                        'x-async': false,
                      },
                    },
                    'x-uid': 'vg6yxbl1suf',
                    'x-async': false,
                  },
                },
                'x-uid': '6hrmi93pzky',
                'x-async': false,
              },
            },
            'x-uid': '4nfpp7jigmt',
            'x-async': false,
          },
        },
        'x-uid': 'kxlzdetp52z',
        'x-async': false,
      },
    },
    'x-uid': 'i56buxw1y1s',
    'x-async': true,
  },
  keepUid: true,
};
