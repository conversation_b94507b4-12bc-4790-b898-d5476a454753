/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const oneFilterFormWithInherit = {
  collections: [
    {
      name: 'parent',
      fields: [
        {
          name: 'parentField1',
          interface: 'input',
        },
        {
          name: 'parentField2',
          interface: 'input',
        },
      ],
    },
    {
      name: 'child',
      fields: [
        {
          name: 'childField1',
          interface: 'input',
        },
        {
          name: 'childField2',
          interface: 'input',
        },
      ],
      inherits: ['parent'],
    },
  ],
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      kq51phui0q9: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          pp7ov10hhjj: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '0.21.0-alpha.15',
            properties: {
              nz5peics4uj: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '0.21.0-alpha.15',
                properties: {
                  ibeh994er9g: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'child',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-filter-operators': {},
                    'x-app-version': '0.21.0-alpha.15',
                    properties: {
                      al2miukt2y0: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '0.21.0-alpha.15',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '0.21.0-alpha.15',
                            'x-uid': 'fs8883bxalw',
                            'x-async': false,
                            'x-index': 1,
                          },
                          ni40rr6efvb: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '0.21.0-alpha.15',
                            'x-uid': 'jhtfu538zve',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'krv6g3fyqwu',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'zne83pn4hvx',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '7s8os6m891x',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'chovlhzi0ee',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': '9xzpdna59re',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'oxtr4rkwpwy',
    'x-async': true,
    'x-index': 1,
  },
};
export const oneFilterFormAndTable = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      '2lkzqn1dzjj': {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          row_1ig46bs6ku1: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-index': 1,
            properties: {
              mtlioslt5qf: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                properties: {
                  d5zxyyhohdo: {
                    'x-uid': '4sibrk213x1',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [
                      {
                        uid: '1rwqxqclb12',
                      },
                    ],
                    'x-app-version': '1.0.1-alpha.1',
                    properties: {
                      lhk3282vrqo: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '1.0.1-alpha.1',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '1.0.1-alpha.1',
                            properties: {
                              c5vjrvqj3de: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.1-alpha.1',
                                properties: {
                                  xy2c8o8t31v: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.1-alpha.1',
                                    properties: {
                                      nickname: {
                                        'x-uid': '8yk8alusq06',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        required: false,
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FilterFormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-use-decorator-props': 'useFormItemProps',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.1-alpha.1',
                                        default: '{{$user.nickname}}',
                                        'x-filter-operator': '$ne',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': '2ws9kipb6nm',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': '0boj0dh7vce',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '78f0wzo8x0j',
                            'x-async': false,
                            'x-index': 1,
                          },
                          wpr1v6jixx9: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '1.0.1-alpha.1',
                            properties: {
                              '91eo3yxhdw9': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Filter") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useFilterBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.1',
                                'x-uid': '2vz3ggxwt4f',
                                'x-async': false,
                                'x-index': 1,
                              },
                              exrihwn6x40: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Reset") }}',
                                'x-component': 'Action',
                                'x-use-component-props': 'useResetBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.1',
                                'x-uid': '57z1lkefr2c',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': '4jn1pt00gev',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': '31hat87y234',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'iyoglhxw19j',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'u4ewva9fbko',
            'x-async': false,
          },
          j70mjkqn4kc: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.1-alpha.1',
            properties: {
              odjq4uaismn: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.1-alpha.1',
                properties: {
                  '4izs6w3tsnp': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.0.1-alpha.1',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.1',
                        'x-uid': '30hb3gzxlai',
                        'x-async': false,
                        'x-index': 1,
                      },
                      q1v94bmwdpj: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.1',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.0.1-alpha.1',
                            properties: {
                              dkxhcxew4oq: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.0.1-alpha.1',
                                'x-uid': '3fmjbc31oh4',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'e0ta0sfrtsf',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '480x01qjjup': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.1-alpha.1',
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.1-alpha.1',
                                'x-uid': '8pzleb85pkt',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '1bcokvneutc',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'snvuqy4ow9p',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': '1rwqxqclb12',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'l91cwfv7uvf',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '8gx4t4hlcfq',
            'x-async': false,
            'x-index': 2,
          },
        },
        'x-uid': 'zuzp6c3ynsg',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 't8vkdftgsi5',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
};
export const oneFilterFormAndTableWithManualLoadingData = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-index': 1,
    properties: {
      an34615vknp: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-index': 1,
        properties: {
          '62fssedpl0w': {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.1-alpha.2',
            'x-index': 1,
            properties: {
              '93tyrk65qym': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.1-alpha.2',
                'x-index': 1,
                properties: {
                  '0b7maevxkfs': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [
                      {
                        uid: 'yg26txxpq4l',
                      },
                    ],
                    'x-app-version': '1.0.1-alpha.2',
                    'x-index': 1,
                    properties: {
                      swmsovm7d4t: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '1.0.1-alpha.2',
                        'x-index': 1,
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '1.0.1-alpha.2',
                            'x-index': 1,
                            properties: {
                              mzd4ludncd8: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.0.1-alpha.2',
                                'x-index': 1,
                                properties: {
                                  w67kg0y8wn6: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.0.1-alpha.2',
                                    'x-index': 1,
                                    properties: {
                                      nickname: {
                                        'x-uid': '3azj6wv3v2r',
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        required: false,
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FilterFormItem',
                                        'x-component': 'CollectionField',
                                        'x-decorator': 'FormItem',
                                        'x-use-decorator-props': 'useFormItemProps',
                                        'x-collection-field': 'users.nickname',
                                        'x-component-props': {},
                                        'x-app-version': '1.0.1-alpha.2',
                                        'x-index': 1,
                                        default: '{{$user.nickname}}',
                                        'x-async': false,
                                      },
                                    },
                                    'x-uid': '1jmg205s2sa',
                                    'x-async': false,
                                  },
                                },
                                'x-uid': 'wjoskqiuk5b',
                                'x-async': false,
                              },
                            },
                            'x-uid': 't3gxqyl52ca',
                            'x-async': false,
                          },
                          ceahs5hahgg: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '1.0.1-alpha.2',
                            'x-index': 2,
                            properties: {
                              xgjhwz2ln8l: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Filter") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useFilterBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.2',
                                'x-index': 1,
                                'x-uid': 'ev40o2gk87b',
                                'x-async': false,
                              },
                              qxhdilp319s: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Reset") }}',
                                'x-component': 'Action',
                                'x-use-component-props': 'useResetBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.0.1-alpha.2',
                                'x-index': 2,
                                'x-uid': '559bivcwabh',
                                'x-async': false,
                              },
                            },
                            'x-uid': '8q25gzurfeh',
                            'x-async': false,
                          },
                        },
                        'x-uid': 'v3mwerc709f',
                        'x-async': false,
                      },
                    },
                    'x-uid': 'uqnziogzhkq',
                    'x-async': false,
                  },
                },
                'x-uid': '952up9gdrhf',
                'x-async': false,
              },
            },
            'x-uid': 'da58ptz1cie',
            'x-async': false,
          },
          dxd8oaoh4a7: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.0.1-alpha.2',
            'x-index': 2,
            properties: {
              '27ijajdwyd2': {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.0.1-alpha.2',
                'x-index': 1,
                properties: {
                  '7yz5da41nt3': {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                        filter: {
                          $and: [
                            {
                              nickname: {
                                $includes: '{{$user.nickname}}',
                              },
                            },
                          ],
                        },
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                      dataLoadingMode: 'manual',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.0.1-alpha.2',
                    'x-index': 1,
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.2',
                        'x-index': 1,
                        'x-uid': '73fy8oxxxk9',
                        'x-async': false,
                      },
                      wo6mmz3gm4e: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.0.1-alpha.2',
                        'x-index': 2,
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.0.1-alpha.2',
                            'x-index': 1,
                            properties: {
                              k96lkyh071r: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.0.1-alpha.2',
                                'x-index': 1,
                                'x-uid': 'e0u8ctx63xa',
                                'x-async': false,
                              },
                            },
                            'x-uid': 'kscb8lws4pt',
                            'x-async': false,
                          },
                          tpeettygzf9: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.0.1-alpha.2',
                            'x-index': 2,
                            properties: {
                              nickname: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.nickname',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  ellipsis: true,
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.0.1-alpha.2',
                                'x-index': 1,
                                'x-uid': 'fyeg48brfk6',
                                'x-async': false,
                              },
                            },
                            'x-uid': '1zzxomha3yb',
                            'x-async': false,
                          },
                        },
                        'x-uid': 'f8zia156lux',
                        'x-async': false,
                      },
                    },
                    'x-uid': 'yg26txxpq4l',
                    'x-async': false,
                  },
                },
                'x-uid': 'j9c5x3pl112',
                'x-async': false,
              },
            },
            'x-uid': 'qkazgqmj5zk',
            'x-async': false,
          },
        },
        'x-uid': 'iabe2fdu20i',
        'x-async': false,
      },
    },
    'x-uid': 'dxf2yvxqvra',
    'x-async': true,
  },
  keepUid: true,
};
export const T4798 = {
  collections: [
    {
      name: 'general',
      title: 'General',
      fields: [
        {
          name: 'manyToOne',
          interface: 'm2o',
          target: 'users',
        },
      ],
    },
  ],
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    'x-app-version': '1.2.20-alpha',
    properties: {
      azl2bh031d4: {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        'x-app-version': '1.2.20-alpha',
        properties: {
          gbq9qs73wwn: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.2.20-alpha',
            properties: {
              a1vhv24xe9p: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.2.20-alpha',
                properties: {
                  ukjf5k8v8ya: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'general',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.2.20-alpha',
                    properties: {
                      '5sxff0jskc3': {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '1.2.20-alpha',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '1.2.20-alpha',
                            properties: {
                              '89zq19tm2in': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-component': 'Grid.Row',
                                'x-app-version': '1.2.20-alpha',
                                properties: {
                                  ygjidym7p66: {
                                    _isJSONSchemaObject: true,
                                    version: '2.0',
                                    type: 'void',
                                    'x-component': 'Grid.Col',
                                    'x-app-version': '1.2.20-alpha',
                                    properties: {
                                      'manyToOne.nickname': {
                                        _isJSONSchemaObject: true,
                                        version: '2.0',
                                        type: 'string',
                                        'x-toolbar': 'FormItemSchemaToolbar',
                                        'x-settings': 'fieldSettings:FilterFormItem',
                                        'x-designer-props': {
                                          interface: 'input',
                                        },
                                        'x-component': 'CollectionField',
                                        'x-read-pretty': false,
                                        'x-decorator': 'FormItem',
                                        'x-collection-field': 'general.manyToOne.nickname',
                                        'x-app-version': '1.2.20-alpha',
                                        'x-uid': 'p9q6a24uv2c',
                                        'x-async': false,
                                        'x-index': 1,
                                      },
                                    },
                                    'x-uid': 'u90miwx3hgl',
                                    'x-async': false,
                                    'x-index': 1,
                                  },
                                },
                                'x-uid': 'bkvwl1qec30',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'wu5tw51gtif',
                            'x-async': false,
                            'x-index': 1,
                          },
                          ypbbpv6r1yx: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '1.2.20-alpha',
                            'x-uid': 'kfkrokmpy5i',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'p2bta9e2our',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-uid': 'gsjxmdmkr4r',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '5a2h74u28a7',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': '892gtpuxa3z',
            'x-async': false,
            'x-index': 1,
          },
        },
        'x-uid': 'sjyf2qxl2qw',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': 'sibnj3xytix',
    'x-async': true,
    'x-index': 1,
  },
};
export const displayManyTOManyAssociationFields = {
  pageSchema: {
    _isJSONSchemaObject: true,
    version: '2.0',
    type: 'void',
    'x-component': 'Page',
    properties: {
      '9r802f25xpw': {
        _isJSONSchemaObject: true,
        version: '2.0',
        type: 'void',
        'x-component': 'Grid',
        'x-initializer': 'page:addBlock',
        properties: {
          y7zqna9ok64: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.3.13-beta',
            properties: {
              e01ptiv7169: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.3.13-beta',
                properties: {
                  '8c03fmw7mv0': {
                    'x-uid': 'osrsw28bg16',
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'FilterFormBlockProvider',
                    'x-use-decorator-props': 'useFilterFormBlockDecoratorProps',
                    'x-decorator-props': {
                      dataSource: 'main',
                      collection: 'users',
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:filterForm',
                    'x-component': 'CardItem',
                    'x-filter-targets': [
                      {
                        uid: 'vl641x6hrd6',
                      },
                    ],
                    'x-app-version': '1.3.13-beta',
                    properties: {
                      vikjl7uaxai: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-component': 'FormV2',
                        'x-use-component-props': 'useFilterFormBlockProps',
                        'x-app-version': '1.3.13-beta',
                        properties: {
                          grid: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-component': 'Grid',
                            'x-initializer': 'filterForm:configureFields',
                            'x-app-version': '1.3.13-beta',
                            'x-uid': 'f38j0uj5vvq',
                            'x-async': false,
                            'x-index': 1,
                          },
                          '0av2ioa3zjr': {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-initializer': 'filterForm:configureActions',
                            'x-component': 'ActionBar',
                            'x-component-props': {
                              layout: 'one-column',
                              style: {
                                float: 'right',
                              },
                            },
                            'x-app-version': '1.3.13-beta',
                            properties: {
                              lsxm71r395m: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Filter") }}',
                                'x-action': 'submit',
                                'x-component': 'Action',
                                'x-use-component-props': 'useFilterBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-component-props': {
                                  type: 'primary',
                                  htmlType: 'submit',
                                },
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.3.13-beta',
                                'x-uid': '6kj3gzqqubb',
                                'x-async': false,
                                'x-index': 1,
                              },
                              '7ukzfx8trmp': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                title: '{{ t("Reset") }}',
                                'x-component': 'Action',
                                'x-use-component-props': 'useResetBlockActionProps',
                                'x-designer': 'Action.Designer',
                                'x-action-settings': {},
                                type: 'void',
                                'x-app-version': '1.3.13-beta',
                                'x-uid': '48rt2qm0rip',
                                'x-async': false,
                                'x-index': 2,
                              },
                            },
                            'x-uid': 'rv5qfqgaxs1',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'pmc05hvyobp',
                        'x-async': false,
                        'x-index': 1,
                      },
                    },
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': 'bzkbofpet3m',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'wlpzuhipi0n',
            'x-async': false,
            'x-index': 1,
          },
          ektxc4qt6td: {
            _isJSONSchemaObject: true,
            version: '2.0',
            type: 'void',
            'x-component': 'Grid.Row',
            'x-app-version': '1.3.13-beta',
            properties: {
              zftl6dfyeia: {
                _isJSONSchemaObject: true,
                version: '2.0',
                type: 'void',
                'x-component': 'Grid.Col',
                'x-app-version': '1.3.13-beta',
                properties: {
                  broo6eq0m2p: {
                    _isJSONSchemaObject: true,
                    version: '2.0',
                    type: 'void',
                    'x-decorator': 'TableBlockProvider',
                    'x-acl-action': 'users:list',
                    'x-use-decorator-props': 'useTableBlockDecoratorProps',
                    'x-decorator-props': {
                      collection: 'users',
                      dataSource: 'main',
                      action: 'list',
                      params: {
                        pageSize: 20,
                      },
                      rowKey: 'id',
                      showIndex: true,
                      dragSort: false,
                    },
                    'x-toolbar': 'BlockSchemaToolbar',
                    'x-settings': 'blockSettings:table',
                    'x-component': 'CardItem',
                    'x-filter-targets': [],
                    'x-app-version': '1.3.13-beta',
                    properties: {
                      actions: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'void',
                        'x-initializer': 'table:configureActions',
                        'x-component': 'ActionBar',
                        'x-component-props': {
                          style: {
                            marginBottom: 'var(--nb-spacing)',
                          },
                        },
                        'x-app-version': '1.3.13-beta',
                        'x-uid': 'w46bhgi0e9b',
                        'x-async': false,
                        'x-index': 1,
                      },
                      c4fknjf3dgj: {
                        _isJSONSchemaObject: true,
                        version: '2.0',
                        type: 'array',
                        'x-initializer': 'table:configureColumns',
                        'x-component': 'TableV2',
                        'x-use-component-props': 'useTableBlockProps',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox',
                          },
                        },
                        'x-app-version': '1.3.13-beta',
                        properties: {
                          actions: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            title: '{{ t("Actions") }}',
                            'x-action-column': 'actions',
                            'x-decorator': 'TableV2.Column.ActionBar',
                            'x-component': 'TableV2.Column',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-initializer': 'table:configureItemActions',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-toolbar-props': {
                              initializer: 'table:configureItemActions',
                            },
                            'x-app-version': '1.3.13-beta',
                            properties: {
                              '5mb0ksk9n7d': {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                type: 'void',
                                'x-decorator': 'DndContext',
                                'x-component': 'Space',
                                'x-component-props': {
                                  split: '|',
                                },
                                'x-app-version': '1.3.13-beta',
                                'x-uid': 'b8lt0cpa8sk',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': 'uygsokuvjjo',
                            'x-async': false,
                            'x-index': 1,
                          },
                          blco479x6xr: {
                            _isJSONSchemaObject: true,
                            version: '2.0',
                            type: 'void',
                            'x-decorator': 'TableV2.Column.Decorator',
                            'x-toolbar': 'TableColumnSchemaToolbar',
                            'x-settings': 'fieldSettings:TableColumn',
                            'x-component': 'TableV2.Column',
                            'x-app-version': '1.3.13-beta',
                            properties: {
                              roles: {
                                _isJSONSchemaObject: true,
                                version: '2.0',
                                'x-collection-field': 'users.roles',
                                'x-component': 'CollectionField',
                                'x-component-props': {
                                  fieldNames: {
                                    value: 'name',
                                    label: 'name',
                                  },
                                  ellipsis: true,
                                  size: 'small',
                                },
                                'x-read-pretty': true,
                                'x-decorator': null,
                                'x-decorator-props': {
                                  labelStyle: {
                                    display: 'none',
                                  },
                                },
                                'x-app-version': '1.3.13-beta',
                                'x-uid': 'gocdkziqwlz',
                                'x-async': false,
                                'x-index': 1,
                              },
                            },
                            'x-uid': '3pfwlp96981',
                            'x-async': false,
                            'x-index': 2,
                          },
                        },
                        'x-uid': 'c6nm8b9emvw',
                        'x-async': false,
                        'x-index': 2,
                      },
                    },
                    'x-uid': 'vl641x6hrd6',
                    'x-async': false,
                    'x-index': 1,
                  },
                },
                'x-uid': '5x1f6kzfwce',
                'x-async': false,
                'x-index': 1,
              },
            },
            'x-uid': 'o9e7bi2jvp7',
            'x-async': false,
            'x-index': 2,
          },
        },
        'x-uid': 'bcez1hvmvm0',
        'x-async': false,
        'x-index': 1,
      },
    },
    'x-uid': '1z5t01zmibi',
    'x-async': true,
    'x-index': 1,
  },
  keepUid: true,
};
